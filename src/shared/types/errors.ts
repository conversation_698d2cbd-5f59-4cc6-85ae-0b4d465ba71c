import { ValidationError } from '@shared/utils/validation';

/**
 * Enhanced error types for better error handling
 */

export enum ErrorCode {
  // Validation errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  TITLE_REQUIRED = 'TITLE_REQUIRED',
  TITLE_TOO_SHORT = 'TITLE_TOO_SHORT',
  TITLE_TOO_LONG = 'TITLE_TOO_LONG',
  DESCRIPTION_TOO_LONG = 'DESCRIPTION_TOO_LONG',
  INVALID_STATUS = 'INVALID_STATUS',
  INVALID_PRIORITY = 'INVALID_PRIORITY',
  INVALID_DATE_FORMAT = 'INVALID_DATE_FORMAT',
  INVALID_DATE = 'INVALID_DATE',
  DUE_DATE_IN_PAST = 'DUE_DATE_IN_PAST',
  DUE_DATE_TOO_FAR = 'DUE_DATE_TOO_FAR',
  REMINDER_IN_PAST = 'REMINDER_IN_PAST',
  REMINDER_AFTER_DUE_DATE = 'REMINDER_AFTER_DUE_DATE',
  INVALID_REMINDER_FORMAT = 'INVALID_REMINDER_FORMAT',
  INVALID_REMINDER_DATE = 'INVALID_REMINDER_DATE',
  TAGS_NOT_ARRAY = 'TAGS_NOT_ARRAY',
  TOO_MANY_TAGS = 'TOO_MANY_TAGS',
  TAG_NOT_STRING = 'TAG_NOT_STRING',
  TAG_EMPTY = 'TAG_EMPTY',
  TAG_TOO_LONG = 'TAG_TOO_LONG',
  DUPLICATE_TAGS = 'DUPLICATE_TAGS',
  INVALID_DURATION_FORMAT = 'INVALID_DURATION_FORMAT',
  DURATION_TOO_LONG = 'DURATION_TOO_LONG',
  DURATION_TOO_SHORT = 'DURATION_TOO_SHORT',
  USER_ID_REQUIRED = 'USER_ID_REQUIRED',
  INVALID_USER_ID_FORMAT = 'INVALID_USER_ID_FORMAT',
  INVALID_CATEGORY_ID_FORMAT = 'INVALID_CATEGORY_ID_FORMAT',
  INVALID_STATUS_TRANSITION = 'INVALID_STATUS_TRANSITION',

  // Database errors
  DATABASE_ERROR = 'DATABASE_ERROR',
  QUERY_ERROR = 'QUERY_ERROR',
  TRANSACTION_ERROR = 'TRANSACTION_ERROR',
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  CONSTRAINT_VIOLATION = 'CONSTRAINT_VIOLATION',
  RECORD_NOT_FOUND = 'RECORD_NOT_FOUND',
  DUPLICATE_RECORD = 'DUPLICATE_RECORD',

  // Authentication errors
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  INVALID_SESSION = 'INVALID_SESSION',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',

  // Business logic errors
  BUSINESS_RULE_VIOLATION = 'BUSINESS_RULE_VIOLATION',
  INVALID_OPERATION = 'INVALID_OPERATION',
  RESOURCE_CONFLICT = 'RESOURCE_CONFLICT',

  // Network/API errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',

  // Unknown/Generic errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR'
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface ErrorContext {
  userId?: string;
  todoId?: string;
  operation?: string;
  timestamp?: Date;
  userAgent?: string;
  sessionId?: string;
  [key: string]: any;
}

/**
 * Enhanced validation error class
 */
export class TodoValidationError extends Error {
  public readonly code: ErrorCode;
  public readonly field?: string;
  public readonly value?: any;
  public readonly severity: ErrorSeverity;
  public readonly context?: ErrorContext;
  public readonly validationErrors: ValidationError[];

  constructor(
    message: string,
    validationErrors: ValidationError[] = [],
    options: {
      code?: ErrorCode;
      field?: string;
      value?: any;
      severity?: ErrorSeverity;
      context?: ErrorContext;
    } = {}
  ) {
    super(message);
    this.name = 'TodoValidationError';
    this.code = options.code || ErrorCode.VALIDATION_ERROR;
    this.field = options.field;
    this.value = options.value;
    this.severity = options.severity || ErrorSeverity.MEDIUM;
    this.context = options.context;
    this.validationErrors = validationErrors;
  }

  /**
   * Get user-friendly error message
   */
  public getUserMessage(): string {
    if (this.validationErrors.length === 1) {
      return this.validationErrors[0].message;
    }
    
    if (this.validationErrors.length > 1) {
      return `Please fix the following issues:\n${this.validationErrors.map(e => `• ${e.message}`).join('\n')}`;
    }

    return this.message;
  }

  /**
   * Get errors for a specific field
   */
  public getFieldErrors(field: string): ValidationError[] {
    return this.validationErrors.filter(error => 
      error.field === field || error.field.startsWith(`${field}[`)
    );
  }

  /**
   * Check if error is for a specific field
   */
  public hasFieldError(field: string): boolean {
    return this.getFieldErrors(field).length > 0;
  }

  /**
   * Convert to JSON for logging/debugging
   */
  public toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      field: this.field,
      value: this.value,
      severity: this.severity,
      context: this.context,
      validationErrors: this.validationErrors,
      stack: this.stack
    };
  }
}

/**
 * Enhanced database error class
 */
export class TodoDatabaseError extends Error {
  public readonly code: ErrorCode;
  public readonly severity: ErrorSeverity;
  public readonly context?: ErrorContext;
  public readonly originalError?: Error;

  constructor(
    message: string,
    options: {
      code?: ErrorCode;
      severity?: ErrorSeverity;
      context?: ErrorContext;
      originalError?: Error;
    } = {}
  ) {
    super(message);
    this.name = 'TodoDatabaseError';
    this.code = options.code || ErrorCode.DATABASE_ERROR;
    this.severity = options.severity || ErrorSeverity.HIGH;
    this.context = options.context;
    this.originalError = options.originalError;
  }

  /**
   * Get user-friendly error message
   */
  public getUserMessage(): string {
    switch (this.code) {
      case ErrorCode.RECORD_NOT_FOUND:
        return 'The requested todo was not found.';
      case ErrorCode.DUPLICATE_RECORD:
        return 'A todo with this information already exists.';
      case ErrorCode.CONNECTION_ERROR:
        return 'Unable to connect to the database. Please try again.';
      case ErrorCode.CONSTRAINT_VIOLATION:
        return 'The operation violates data constraints.';
      default:
        return 'A database error occurred. Please try again.';
    }
  }

  /**
   * Convert to JSON for logging/debugging
   */
  public toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      severity: this.severity,
      context: this.context,
      originalError: this.originalError?.message,
      stack: this.stack
    };
  }
}

/**
 * Enhanced business logic error class
 */
export class TodoBusinessError extends Error {
  public readonly code: ErrorCode;
  public readonly severity: ErrorSeverity;
  public readonly context?: ErrorContext;

  constructor(
    message: string,
    options: {
      code?: ErrorCode;
      severity?: ErrorSeverity;
      context?: ErrorContext;
    } = {}
  ) {
    super(message);
    this.name = 'TodoBusinessError';
    this.code = options.code || ErrorCode.BUSINESS_RULE_VIOLATION;
    this.severity = options.severity || ErrorSeverity.MEDIUM;
    this.context = options.context;
  }

  /**
   * Convert to JSON for logging/debugging
   */
  public toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      severity: this.severity,
      context: this.context,
      stack: this.stack
    };
  }
}

/**
 * Error factory functions
 */
export class TodoErrorFactory {
  static createValidationError(
    validationErrors: ValidationError[],
    context?: ErrorContext
  ): TodoValidationError {
    const message = validationErrors.length === 1 
      ? validationErrors[0].message
      : `${validationErrors.length} validation errors occurred`;

    return new TodoValidationError(message, validationErrors, {
      code: ErrorCode.VALIDATION_ERROR,
      context
    });
  }

  static createDatabaseError(
    message: string,
    originalError?: Error,
    context?: ErrorContext
  ): TodoDatabaseError {
    return new TodoDatabaseError(message, {
      code: ErrorCode.DATABASE_ERROR,
      originalError,
      context
    });
  }

  static createNotFoundError(
    resource: string = 'Todo',
    context?: ErrorContext
  ): TodoDatabaseError {
    return new TodoDatabaseError(`${resource} not found`, {
      code: ErrorCode.RECORD_NOT_FOUND,
      severity: ErrorSeverity.MEDIUM,
      context
    });
  }

  static createBusinessError(
    message: string,
    context?: ErrorContext
  ): TodoBusinessError {
    return new TodoBusinessError(message, {
      code: ErrorCode.BUSINESS_RULE_VIOLATION,
      context
    });
  }
}
