import { Todo, TodoStatus, TodoPriority } from '@shared/types';

// Validation error types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: any;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Constants for validation rules
export const VALIDATION_RULES = {
  TITLE: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 500,
    REQUIRED: true
  },
  DESCRIPTION: {
    MAX_LENGTH: 5000
  },
  TAGS: {
    MAX_COUNT: 20,
    MAX_TAG_LENGTH: 50,
    MIN_TAG_LENGTH: 1
  },
  ESTIMATED_DURATION: {
    MAX_HOURS: 999,
    MIN_MINUTES: 1
  }
} as const;

// Valid enum values
export const VALID_TODO_STATUSES: TodoStatus[] = ['pending', 'in_progress', 'completed', 'archived', 'cancelled'];
export const VALID_TODO_PRIORITIES: TodoPriority[] = ['very_low', 'low', 'medium', 'high', 'very_high'];

/**
 * Sanitize and normalize string input
 */
export function sanitizeString(input: string | undefined | null): string {
  if (!input) return '';
  return input.trim().replace(/\s+/g, ' ');
}

/**
 * Validate todo title
 */
export function validateTitle(title: string | undefined | null): ValidationError[] {
  const errors: ValidationError[] = [];
  const sanitized = sanitizeString(title);

  if (VALIDATION_RULES.TITLE.REQUIRED && !sanitized) {
    errors.push({
      field: 'title',
      message: 'Title is required',
      code: 'TITLE_REQUIRED',
      value: title
    });
    return errors;
  }

  if (sanitized.length < VALIDATION_RULES.TITLE.MIN_LENGTH) {
    errors.push({
      field: 'title',
      message: `Title must be at least ${VALIDATION_RULES.TITLE.MIN_LENGTH} character long`,
      code: 'TITLE_TOO_SHORT',
      value: title
    });
  }

  if (sanitized.length > VALIDATION_RULES.TITLE.MAX_LENGTH) {
    errors.push({
      field: 'title',
      message: `Title cannot exceed ${VALIDATION_RULES.TITLE.MAX_LENGTH} characters`,
      code: 'TITLE_TOO_LONG',
      value: title
    });
  }

  return errors;
}

/**
 * Validate todo description
 */
export function validateDescription(description: string | undefined | null): ValidationError[] {
  const errors: ValidationError[] = [];
  const sanitized = sanitizeString(description);

  if (sanitized && sanitized.length > VALIDATION_RULES.DESCRIPTION.MAX_LENGTH) {
    errors.push({
      field: 'description',
      message: `Description cannot exceed ${VALIDATION_RULES.DESCRIPTION.MAX_LENGTH} characters`,
      code: 'DESCRIPTION_TOO_LONG',
      value: description
    });
  }

  return errors;
}

/**
 * Validate todo status
 */
export function validateStatus(status: string | undefined | null): ValidationError[] {
  const errors: ValidationError[] = [];

  if (status && !VALID_TODO_STATUSES.includes(status as TodoStatus)) {
    errors.push({
      field: 'status',
      message: `Invalid status. Must be one of: ${VALID_TODO_STATUSES.join(', ')}`,
      code: 'INVALID_STATUS',
      value: status
    });
  }

  return errors;
}

/**
 * Validate todo priority
 */
export function validatePriority(priority: string | undefined | null): ValidationError[] {
  const errors: ValidationError[] = [];

  if (priority && !VALID_TODO_PRIORITIES.includes(priority as TodoPriority)) {
    errors.push({
      field: 'priority',
      message: `Invalid priority. Must be one of: ${VALID_TODO_PRIORITIES.join(', ')}`,
      code: 'INVALID_PRIORITY',
      value: priority
    });
  }

  return errors;
}

/**
 * Validate due date
 */
export function validateDueDate(dueDate: Date | string | undefined | null, allowPastDates = true): ValidationError[] {
  const errors: ValidationError[] = [];

  if (!dueDate) return errors;

  let date: Date;
  try {
    date = typeof dueDate === 'string' ? new Date(dueDate) : dueDate;
  } catch {
    errors.push({
      field: 'due_date',
      message: 'Invalid date format',
      code: 'INVALID_DATE_FORMAT',
      value: dueDate
    });
    return errors;
  }

  if (isNaN(date.getTime())) {
    errors.push({
      field: 'due_date',
      message: 'Invalid date',
      code: 'INVALID_DATE',
      value: dueDate
    });
    return errors;
  }

  if (!allowPastDates && date < new Date()) {
    errors.push({
      field: 'due_date',
      message: 'Due date cannot be in the past',
      code: 'DUE_DATE_IN_PAST',
      value: dueDate
    });
  }

  // Check for reasonable date range (not too far in the future)
  const maxDate = new Date();
  maxDate.setFullYear(maxDate.getFullYear() + 10);
  if (date > maxDate) {
    errors.push({
      field: 'due_date',
      message: 'Due date is too far in the future',
      code: 'DUE_DATE_TOO_FAR',
      value: dueDate
    });
  }

  return errors;
}

/**
 * Validate reminder date
 */
export function validateReminderDate(reminderDate: Date | string | undefined | null, dueDate?: Date | string | null): ValidationError[] {
  const errors: ValidationError[] = [];

  if (!reminderDate) return errors;

  let reminder: Date;
  try {
    reminder = typeof reminderDate === 'string' ? new Date(reminderDate) : reminderDate;
  } catch {
    errors.push({
      field: 'reminder_at',
      message: 'Invalid reminder date format',
      code: 'INVALID_REMINDER_FORMAT',
      value: reminderDate
    });
    return errors;
  }

  if (isNaN(reminder.getTime())) {
    errors.push({
      field: 'reminder_at',
      message: 'Invalid reminder date',
      code: 'INVALID_REMINDER_DATE',
      value: reminderDate
    });
    return errors;
  }

  // Reminder should not be in the past
  if (reminder < new Date()) {
    errors.push({
      field: 'reminder_at',
      message: 'Reminder date cannot be in the past',
      code: 'REMINDER_IN_PAST',
      value: reminderDate
    });
  }

  // If due date is provided, reminder should be before due date
  if (dueDate) {
    const due = typeof dueDate === 'string' ? new Date(dueDate) : dueDate;
    if (!isNaN(due.getTime()) && reminder > due) {
      errors.push({
        field: 'reminder_at',
        message: 'Reminder date cannot be after due date',
        code: 'REMINDER_AFTER_DUE_DATE',
        value: reminderDate
      });
    }
  }

  return errors;
}

/**
 * Validate tags array
 */
export function validateTags(tags: string[] | undefined | null): ValidationError[] {
  const errors: ValidationError[] = [];

  if (!tags) return errors;

  if (!Array.isArray(tags)) {
    errors.push({
      field: 'tags',
      message: 'Tags must be an array',
      code: 'TAGS_NOT_ARRAY',
      value: tags
    });
    return errors;
  }

  if (tags.length > VALIDATION_RULES.TAGS.MAX_COUNT) {
    errors.push({
      field: 'tags',
      message: `Cannot have more than ${VALIDATION_RULES.TAGS.MAX_COUNT} tags`,
      code: 'TOO_MANY_TAGS',
      value: tags
    });
  }

  // Validate individual tags
  tags.forEach((tag, index) => {
    if (typeof tag !== 'string') {
      errors.push({
        field: `tags[${index}]`,
        message: 'Tag must be a string',
        code: 'TAG_NOT_STRING',
        value: tag
      });
      return;
    }

    const sanitized = sanitizeString(tag);
    if (sanitized.length < VALIDATION_RULES.TAGS.MIN_TAG_LENGTH) {
      errors.push({
        field: `tags[${index}]`,
        message: `Tag cannot be empty`,
        code: 'TAG_EMPTY',
        value: tag
      });
    }

    if (sanitized.length > VALIDATION_RULES.TAGS.MAX_TAG_LENGTH) {
      errors.push({
        field: `tags[${index}]`,
        message: `Tag cannot exceed ${VALIDATION_RULES.TAGS.MAX_TAG_LENGTH} characters`,
        code: 'TAG_TOO_LONG',
        value: tag
      });
    }
  });

  // Check for duplicate tags
  const uniqueTags = new Set(tags.map(tag => sanitizeString(tag).toLowerCase()));
  if (uniqueTags.size !== tags.length) {
    errors.push({
      field: 'tags',
      message: 'Duplicate tags are not allowed',
      code: 'DUPLICATE_TAGS',
      value: tags
    });
  }

  return errors;
}

/**
 * Validate estimated duration (ISO 8601 duration string)
 */
export function validateEstimatedDuration(duration: string | undefined | null): ValidationError[] {
  const errors: ValidationError[] = [];

  if (!duration) return errors;

  // Basic ISO 8601 duration format validation (PT1H30M)
  const durationRegex = /^PT(?:(\d+)H)?(?:(\d+)M)?$/;
  const match = duration.match(durationRegex);

  if (!match) {
    errors.push({
      field: 'estimated_duration',
      message: 'Invalid duration format. Use format like PT1H30M (1 hour 30 minutes)',
      code: 'INVALID_DURATION_FORMAT',
      value: duration
    });
    return errors;
  }

  const hours = parseInt(match[1] || '0', 10);
  const minutes = parseInt(match[2] || '0', 10);

  if (hours > VALIDATION_RULES.ESTIMATED_DURATION.MAX_HOURS) {
    errors.push({
      field: 'estimated_duration',
      message: `Duration cannot exceed ${VALIDATION_RULES.ESTIMATED_DURATION.MAX_HOURS} hours`,
      code: 'DURATION_TOO_LONG',
      value: duration
    });
  }

  if (hours === 0 && minutes < VALIDATION_RULES.ESTIMATED_DURATION.MIN_MINUTES) {
    errors.push({
      field: 'estimated_duration',
      message: `Duration must be at least ${VALIDATION_RULES.ESTIMATED_DURATION.MIN_MINUTES} minute`,
      code: 'DURATION_TOO_SHORT',
      value: duration
    });
  }

  return errors;
}

/**
 * Validate user ID
 */
export function validateUserId(userId: string | undefined | null): ValidationError[] {
  const errors: ValidationError[] = [];

  if (!userId) {
    errors.push({
      field: 'user_id',
      message: 'User ID is required',
      code: 'USER_ID_REQUIRED',
      value: userId
    });
    return errors;
  }

  // Basic UUID format validation
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(userId)) {
    errors.push({
      field: 'user_id',
      message: 'Invalid user ID format',
      code: 'INVALID_USER_ID_FORMAT',
      value: userId
    });
  }

  return errors;
}

/**
 * Validate category ID
 */
export function validateCategoryId(categoryId: string | undefined | null): ValidationError[] {
  const errors: ValidationError[] = [];

  if (!categoryId) return errors; // Category is optional

  // Basic UUID format validation
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(categoryId)) {
    errors.push({
      field: 'category_id',
      message: 'Invalid category ID format',
      code: 'INVALID_CATEGORY_ID_FORMAT',
      value: categoryId
    });
  }

  return errors;
}

/**
 * Sanitize and normalize todo data
 */
export function sanitizeTodoData(data: Partial<Todo>): Partial<Todo> {
  const sanitized: Partial<Todo> = { ...data };

  // Sanitize string fields
  if (sanitized.title !== undefined) {
    sanitized.title = sanitizeString(sanitized.title);
  }

  if (sanitized.description !== undefined) {
    sanitized.description = sanitizeString(sanitized.description) || undefined;
  }

  // Sanitize tags
  if (sanitized.tags) {
    sanitized.tags = sanitized.tags
      .map(tag => sanitizeString(tag))
      .filter(tag => tag.length > 0)
      .filter((tag, index, arr) => arr.indexOf(tag) === index); // Remove duplicates
  }

  // Ensure metadata is an object
  if (sanitized.metadata === undefined) {
    sanitized.metadata = {};
  } else if (typeof sanitized.metadata !== 'object' || sanitized.metadata === null) {
    sanitized.metadata = {};
  }

  return sanitized;
}

/**
 * Comprehensive todo validation
 */
export function validateTodo(data: Partial<Todo>, options: {
  isCreate?: boolean;
  allowPastDueDates?: boolean;
  requireUserId?: boolean;
} = {}): ValidationResult {
  const {
    isCreate = false,
    allowPastDueDates = true,
    requireUserId = true
  } = options;

  const errors: ValidationError[] = [];

  // Validate required fields for creation
  if (isCreate || data.title !== undefined) {
    errors.push(...validateTitle(data.title));
  }

  if (requireUserId && (isCreate || data.user_id !== undefined)) {
    errors.push(...validateUserId(data.user_id));
  }

  // Validate optional fields if provided
  if (data.description !== undefined) {
    errors.push(...validateDescription(data.description));
  }

  if (data.status !== undefined) {
    errors.push(...validateStatus(data.status));
  }

  if (data.priority !== undefined) {
    errors.push(...validatePriority(data.priority));
  }

  if (data.due_date !== undefined) {
    errors.push(...validateDueDate(data.due_date, allowPastDueDates));
  }

  if (data.reminder_at !== undefined) {
    errors.push(...validateReminderDate(data.reminder_at, data.due_date));
  }

  if (data.tags !== undefined) {
    errors.push(...validateTags(data.tags));
  }

  if (data.estimated_duration !== undefined) {
    errors.push(...validateEstimatedDuration(data.estimated_duration));
  }

  if (data.category_id !== undefined) {
    errors.push(...validateCategoryId(data.category_id));
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate todo status transition
 */
export function validateStatusTransition(
  currentStatus: TodoStatus,
  newStatus: TodoStatus
): ValidationError[] {
  const errors: ValidationError[] = [];

  // Define valid transitions
  const validTransitions: Record<TodoStatus, TodoStatus[]> = {
    pending: ['in_progress', 'completed', 'cancelled'],
    in_progress: ['pending', 'completed', 'cancelled'],
    completed: ['pending', 'in_progress', 'archived'],
    archived: ['pending', 'in_progress'],
    cancelled: ['pending', 'in_progress']
  };

  if (!validTransitions[currentStatus]?.includes(newStatus)) {
    errors.push({
      field: 'status',
      message: `Cannot transition from ${currentStatus} to ${newStatus}`,
      code: 'INVALID_STATUS_TRANSITION',
      value: { from: currentStatus, to: newStatus }
    });
  }

  return errors;
}

/**
 * Create user-friendly error message from validation errors
 */
export function formatValidationErrors(errors: ValidationError[]): string {
  if (errors.length === 0) return '';

  if (errors.length === 1) {
    return errors[0].message;
  }

  return `Multiple validation errors:\n${errors.map(e => `• ${e.message}`).join('\n')}`;
}

/**
 * Get validation errors for a specific field
 */
export function getFieldErrors(errors: ValidationError[], field: string): ValidationError[] {
  return errors.filter(error => error.field === field || error.field.startsWith(`${field}[`));
}
