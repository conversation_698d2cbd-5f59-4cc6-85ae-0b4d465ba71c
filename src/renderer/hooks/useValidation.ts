import { useState, useCallback } from 'react';
import { 
  validateTodo, 
  sanitizeTodoData, 
  ValidationError, 
  ValidationResult,
  formatValidationErrors,
  getFieldErrors
} from '@shared/types';
import { Todo } from '@shared/types';

export interface UseValidationOptions {
  isCreate?: boolean;
  allowPastDueDates?: boolean;
  requireUserId?: boolean;
}

export interface ValidationState {
  isValid: boolean;
  errors: ValidationError[];
  fieldErrors: Record<string, ValidationError[]>;
  hasErrors: boolean;
  isValidating: boolean;
}

export interface UseValidationReturn {
  validationState: ValidationState;
  validateData: (data: Partial<Todo>) => ValidationResult;
  validateField: (field: string, value: any, data?: Partial<Todo>) => ValidationError[];
  clearValidation: () => void;
  clearFieldErrors: (field: string) => void;
  getFieldErrorMessage: (field: string) => string | null;
  hasFieldError: (field: string) => boolean;
  sanitizeData: (data: Partial<Todo>) => Partial<Todo>;
}

/**
 * Custom hook for todo validation with real-time feedback
 */
export function useValidation(options: UseValidationOptions = {}): UseValidationReturn {
  const [validationState, setValidationState] = useState<ValidationState>({
    isValid: true,
    errors: [],
    fieldErrors: {},
    hasErrors: false,
    isValidating: false
  });

  const updateValidationState = useCallback((result: ValidationResult) => {
    const fieldErrors: Record<string, ValidationError[]> = {};
    
    // Group errors by field
    result.errors.forEach(error => {
      const field = error.field;
      if (!fieldErrors[field]) {
        fieldErrors[field] = [];
      }
      fieldErrors[field].push(error);
    });

    setValidationState({
      isValid: result.isValid,
      errors: result.errors,
      fieldErrors,
      hasErrors: result.errors.length > 0,
      isValidating: false
    });
  }, []);

  const validateData = useCallback((data: Partial<Todo>): ValidationResult => {
    setValidationState(prev => ({ ...prev, isValidating: true }));
    
    const sanitizedData = sanitizeTodoData(data);
    const result = validateTodo(sanitizedData, options);
    
    updateValidationState(result);
    return result;
  }, [options, updateValidationState]);

  const validateField = useCallback((
    field: string, 
    value: any, 
    data: Partial<Todo> = {}
  ): ValidationError[] => {
    // Create a partial data object with just the field being validated
    const fieldData = { ...data, [field]: value };
    const sanitizedData = sanitizeTodoData(fieldData);
    const result = validateTodo(sanitizedData, { ...options, isCreate: false });
    
    // Return only errors for the specific field
    return getFieldErrors(result.errors, field);
  }, [options]);

  const clearValidation = useCallback(() => {
    setValidationState({
      isValid: true,
      errors: [],
      fieldErrors: {},
      hasErrors: false,
      isValidating: false
    });
  }, []);

  const clearFieldErrors = useCallback((field: string) => {
    setValidationState(prev => {
      const newFieldErrors = { ...prev.fieldErrors };
      delete newFieldErrors[field];
      
      const remainingErrors = prev.errors.filter(error => 
        !error.field.startsWith(field) && error.field !== field
      );

      return {
        ...prev,
        errors: remainingErrors,
        fieldErrors: newFieldErrors,
        hasErrors: remainingErrors.length > 0,
        isValid: remainingErrors.length === 0
      };
    });
  }, []);

  const getFieldErrorMessage = useCallback((field: string): string | null => {
    const fieldErrors = validationState.fieldErrors[field];
    if (!fieldErrors || fieldErrors.length === 0) {
      return null;
    }
    
    if (fieldErrors.length === 1) {
      return fieldErrors[0].message;
    }
    
    return fieldErrors.map(error => error.message).join(', ');
  }, [validationState.fieldErrors]);

  const hasFieldError = useCallback((field: string): boolean => {
    return !!(validationState.fieldErrors[field]?.length > 0);
  }, [validationState.fieldErrors]);

  const sanitizeData = useCallback((data: Partial<Todo>): Partial<Todo> => {
    return sanitizeTodoData(data);
  }, []);

  return {
    validationState,
    validateData,
    validateField,
    clearValidation,
    clearFieldErrors,
    getFieldErrorMessage,
    hasFieldError,
    sanitizeData
  };
}

/**
 * Hook for real-time field validation
 */
export function useFieldValidation(
  field: string,
  options: UseValidationOptions = {}
) {
  const [fieldErrors, setFieldErrors] = useState<ValidationError[]>([]);
  const [isValidating, setIsValidating] = useState(false);

  const validateField = useCallback(async (
    value: any,
    data: Partial<Todo> = {}
  ): Promise<ValidationError[]> => {
    setIsValidating(true);
    
    try {
      // Create a partial data object with just the field being validated
      const fieldData = { ...data, [field]: value };
      const sanitizedData = sanitizeTodoData(fieldData);
      const result = validateTodo(sanitizedData, { ...options, isCreate: false });
      
      // Get only errors for this specific field
      const errors = getFieldErrors(result.errors, field);
      setFieldErrors(errors);
      
      return errors;
    } finally {
      setIsValidating(false);
    }
  }, [field, options]);

  const clearErrors = useCallback(() => {
    setFieldErrors([]);
  }, []);

  const hasError = fieldErrors.length > 0;
  const errorMessage = fieldErrors.length > 0 
    ? (fieldErrors.length === 1 ? fieldErrors[0].message : fieldErrors.map(e => e.message).join(', '))
    : null;

  return {
    fieldErrors,
    hasError,
    errorMessage,
    isValidating,
    validateField,
    clearErrors
  };
}

/**
 * Hook for form-level validation with debouncing
 */
export function useFormValidation(
  options: UseValidationOptions = {},
  debounceMs: number = 300
) {
  const validation = useValidation(options);
  const [debouncedValidation, setDebouncedValidation] = useState<ValidationState>(validation.validationState);

  // Debounce validation updates
  const debounceTimeout = useState<NodeJS.Timeout | null>(null);

  const debouncedValidateData = useCallback((data: Partial<Todo>): ValidationResult => {
    const result = validation.validateData(data);
    
    // Clear existing timeout
    if (debounceTimeout[0]) {
      clearTimeout(debounceTimeout[0]);
    }
    
    // Set new timeout for debounced update
    debounceTimeout[0] = setTimeout(() => {
      setDebouncedValidation(validation.validationState);
    }, debounceMs);
    
    return result;
  }, [validation, debounceMs]);

  return {
    ...validation,
    validationState: debouncedValidation,
    validateData: debouncedValidateData
  };
}
