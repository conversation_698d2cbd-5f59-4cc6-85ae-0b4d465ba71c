import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { persist } from 'zustand/middleware';

// Theme types
export type Theme = 'light' | 'dark' | 'frutiger-aero';

// View types
export type ViewMode = 'list' | 'grid' | 'kanban';
export type CurrentView = 'todos' | 'categories' | 'analytics' | 'settings';

// Modal types
export type ModalType = 'todo-form' | 'category-form' | 'settings' | 'confirm-delete' | 'bulk-actions' | null;

// Notification types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number; // in milliseconds, null for persistent
  actions?: NotificationAction[];
  createdAt: Date;
}

export interface NotificationAction {
  label: string;
  action: () => void;
  variant?: 'primary' | 'secondary';
}

// Loading state types
export interface LoadingState {
  [key: string]: boolean;
}

// UI state interface
export interface UIState {
  // Theme and appearance
  theme: Theme;
  sidebarOpen: boolean;
  sidebarWidth: number;
  
  // Modal management
  activeModal: ModalType;
  modalData: any;
  
  // Notifications
  notifications: Notification[];
  
  // Loading states
  globalLoading: boolean;
  loadingStates: LoadingState;
  
  // View management
  currentView: CurrentView;
  previousView: CurrentView | null;
  todoViewMode: ViewMode;
  showCompletedTodos: boolean;
  
  // User preferences
  autoSaveEnabled: boolean;
  compactMode: boolean;
  showNotifications: boolean;
  soundEnabled: boolean;
  
  // Actions - Theme Management
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  
  // Actions - Sidebar Management
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
  setSidebarWidth: (width: number) => void;
  
  // Actions - Modal Management
  openModal: (type: ModalType, data?: any) => void;
  closeModal: () => void;
  
  // Actions - Notification Management
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => string;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  
  // Actions - Loading State Management
  setGlobalLoading: (loading: boolean) => void;
  setLoadingState: (key: string, loading: boolean) => void;
  clearLoadingState: (key: string) => void;
  
  // Actions - View Management
  setCurrentView: (view: CurrentView) => void;
  setTodoViewMode: (mode: ViewMode) => void;
  toggleCompletedTodos: () => void;
  
  // Actions - Preferences
  setAutoSave: (enabled: boolean) => void;
  setCompactMode: (enabled: boolean) => void;
  setShowNotifications: (enabled: boolean) => void;
  setSoundEnabled: (enabled: boolean) => void;
  
  // Computed selectors
  getActiveNotifications: () => Notification[];
  isLoading: (key?: string) => boolean;
}

// Helper functions
const generateNotificationId = () => `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

const getNextTheme = (currentTheme: Theme): Theme => {
  const themes: Theme[] = ['light', 'dark', 'frutiger-aero'];
  const currentIndex = themes.indexOf(currentTheme);
  return themes[(currentIndex + 1) % themes.length];
};

// Create the UI store
export const useUIStore = create<UIState>()(
  subscribeWithSelector(
    immer(
      persist(
        (set, get) => ({
          // Initial state
          theme: 'frutiger-aero',
          sidebarOpen: true,
          sidebarWidth: 280,
          activeModal: null,
          modalData: null,
          notifications: [],
          globalLoading: false,
          loadingStates: {},
          currentView: 'todos',
          previousView: null,
          todoViewMode: 'list',
          showCompletedTodos: true,
          autoSaveEnabled: true,
          compactMode: false,
          showNotifications: true,
          soundEnabled: true,

          // Theme Management Actions
          setTheme: (theme) => {
            set((state) => {
              state.theme = theme;
            });
            
            // Apply theme to document
            document.documentElement.setAttribute('data-theme', theme);
            
            // Store in localStorage for immediate access
            localStorage.setItem('fa-theme', theme);
          },

          toggleTheme: () => {
            const currentTheme = get().theme;
            const nextTheme = getNextTheme(currentTheme);
            get().setTheme(nextTheme);
          },

          // Sidebar Management Actions
          toggleSidebar: () => {
            set((state) => {
              state.sidebarOpen = !state.sidebarOpen;
            });
          },

          setSidebarOpen: (open) => {
            set((state) => {
              state.sidebarOpen = open;
            });
          },

          setSidebarWidth: (width) => {
            set((state) => {
              state.sidebarWidth = Math.max(200, Math.min(400, width));
            });
          },

          // Modal Management Actions
          openModal: (type, data = null) => {
            set((state) => {
              state.activeModal = type;
              state.modalData = data;
            });
          },

          closeModal: () => {
            set((state) => {
              state.activeModal = null;
              state.modalData = null;
            });
          },

          // Notification Management Actions
          addNotification: (notificationData) => {
            const id = generateNotificationId();
            const notification: Notification = {
              id,
              ...notificationData,
              createdAt: new Date(),
            };

            set((state) => {
              state.notifications.push(notification);
            });

            // Auto-remove notification after duration (if specified)
            if (notification.duration && notification.duration > 0) {
              setTimeout(() => {
                get().removeNotification(id);
              }, notification.duration);
            }

            return id;
          },

          removeNotification: (id) => {
            set((state) => {
              state.notifications = state.notifications.filter(n => n.id !== id);
            });
          },

          clearNotifications: () => {
            set((state) => {
              state.notifications = [];
            });
          },

          // Loading State Management Actions
          setGlobalLoading: (loading) => {
            set((state) => {
              state.globalLoading = loading;
            });
          },

          setLoadingState: (key, loading) => {
            set((state) => {
              if (loading) {
                state.loadingStates[key] = true;
              } else {
                delete state.loadingStates[key];
              }
            });
          },

          clearLoadingState: (key) => {
            set((state) => {
              delete state.loadingStates[key];
            });
          },

          // View Management Actions
          setCurrentView: (view) => {
            set((state) => {
              state.previousView = state.currentView;
              state.currentView = view;
            });
          },

          setTodoViewMode: (mode) => {
            set((state) => {
              state.todoViewMode = mode;
            });
          },

          toggleCompletedTodos: () => {
            set((state) => {
              state.showCompletedTodos = !state.showCompletedTodos;
            });
          },

          // Preferences Actions
          setAutoSave: (enabled) => {
            set((state) => {
              state.autoSaveEnabled = enabled;
            });
          },

          setCompactMode: (enabled) => {
            set((state) => {
              state.compactMode = enabled;
            });
          },

          setShowNotifications: (enabled) => {
            set((state) => {
              state.showNotifications = enabled;
            });
          },

          setSoundEnabled: (enabled) => {
            set((state) => {
              state.soundEnabled = enabled;
            });
          },

          // Computed selectors
          getActiveNotifications: () => {
            const { notifications, showNotifications } = get();
            return showNotifications ? notifications : [];
          },

          isLoading: (key) => {
            const { globalLoading, loadingStates } = get();
            if (key) {
              return loadingStates[key] || false;
            }
            return globalLoading || Object.keys(loadingStates).length > 0;
          },
        }),
        {
          name: 'ui-store',
          partialize: (state) => ({
            theme: state.theme,
            sidebarOpen: state.sidebarOpen,
            sidebarWidth: state.sidebarWidth,
            currentView: state.currentView,
            todoViewMode: state.todoViewMode,
            showCompletedTodos: state.showCompletedTodos,
            autoSaveEnabled: state.autoSaveEnabled,
            compactMode: state.compactMode,
            showNotifications: state.showNotifications,
            soundEnabled: state.soundEnabled,
          }),
        }
      )
    )
  )
);

// Notification helper functions
export const useNotifications = () => {
  const addNotification = useUIStore(state => state.addNotification);
  
  const showSuccess = (title: string, message: string, duration = 5000) => {
    return addNotification({
      type: 'success',
      title,
      message,
      duration,
    });
  };

  const showError = (title: string, message: string, duration = 8000) => {
    return addNotification({
      type: 'error',
      title,
      message,
      duration,
    });
  };

  const showWarning = (title: string, message: string, duration = 6000) => {
    return addNotification({
      type: 'warning',
      title,
      message,
      duration,
    });
  };

  const showInfo = (title: string, message: string, duration = 5000) => {
    return addNotification({
      type: 'info',
      title,
      message,
      duration,
    });
  };

  return {
    showSuccess,
    showError,
    showWarning,
    showInfo,
  };
};

// Theme initialization helper
export const initializeTheme = () => {
  const setTheme = useUIStore.getState().setTheme;
  const savedTheme = localStorage.getItem('fa-theme') as Theme;
  
  if (savedTheme) {
    setTheme(savedTheme);
  } else {
    // Detect system theme preference
    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    setTheme(systemTheme);
  }
};
