import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { persist } from 'zustand/middleware';
import { Todo, Category, TodoStatus, TodoPriority } from '@shared/types';
import { todoService } from '@renderer/services/todo.service';
import { categoryService, CreateCategoryRequest, UpdateCategoryRequest } from '@renderer/services/category.service';

// Filter interfaces
export interface TodoFilters {
  status: TodoStatus[];
  priority: TodoPriority[];
  categoryId: string[];
  tags: string[];
  dueDateRange: {
    start?: Date;
    end?: Date;
  };
  searchQuery: string;
  sortBy: 'position' | 'created_at' | 'updated_at' | 'due_date' | 'priority' | 'title';
  sortOrder: 'asc' | 'desc';
}

// Store state interface
export interface TodoState {
  // Data
  todos: Todo[];
  categories: Category[];
  filters: TodoFilters;
  
  // UI State
  isLoading: boolean;
  error: string | null;
  selectedTodos: string[];
  draggedTodo: string | null;
  
  // Statistics
  totalCount: number;
  completedCount: number;
  pendingCount: number;
  
  // Actions - Todo Management
  loadTodos: () => Promise<void>;
  createTodo: (todo: Omit<Todo, 'id' | 'created_at' | 'updated_at'>) => Promise<string>;
  updateTodo: (id: string, updates: Partial<Todo>) => Promise<void>;
  deleteTodo: (id: string) => Promise<void>;
  toggleTodoComplete: (id: string) => Promise<void>;
  reorderTodos: (sourceIndex: number, destinationIndex: number, categoryId?: string) => Promise<void>;
  
  // Actions - Category Management
  loadCategories: () => Promise<void>;
  createCategory: (categoryData: CreateCategoryRequest) => Promise<string>;
  updateCategory: (id: string, updates: UpdateCategoryRequest) => Promise<void>;
  deleteCategory: (id: string) => Promise<void>;
  
  // Actions - Filter Management
  setFilters: (filters: Partial<TodoFilters>) => void;
  clearFilters: () => void;
  setSearchQuery: (query: string) => void;
  setSortConfig: (sortBy: TodoFilters['sortBy'], sortOrder: TodoFilters['sortOrder']) => void;
  
  // Actions - Selection Management
  selectTodo: (id: string) => void;
  deselectTodo: (id: string) => void;
  selectAllTodos: () => void;
  clearSelection: () => void;
  toggleTodoSelection: (id: string) => void;
  
  // Actions - Drag and Drop
  setDraggedTodo: (id: string | null) => void;
  
  // Actions - Tag Management
  getAllTags: () => string[];
  bulkUpdateTags: (oldTag: string, newTag: string) => Promise<void>;
  deleteTag: (tag: string) => Promise<void>;

  // Computed selectors
  getFilteredTodos: () => Todo[];
  getTodosByCategory: (categoryId: string) => Todo[];
  getCompletedTodos: () => Todo[];
  getPendingTodos: () => Todo[];
  getOverdueTodos: () => Todo[];
  getTodoStats: () => { total: number; completed: number; pending: number; overdue: number };
}

// Default filters
const defaultFilters: TodoFilters = {
  status: [],
  priority: [],
  categoryId: [],
  tags: [],
  dueDateRange: {},
  searchQuery: '',
  sortBy: 'position',
  sortOrder: 'asc',
};

// Helper functions
const generateTempId = () => `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

const filterTodos = (todos: Todo[], filters: TodoFilters): Todo[] => {
  return todos.filter(todo => {
    // Status filter
    if (filters.status.length > 0 && !filters.status.includes(todo.status)) {
      return false;
    }
    
    // Priority filter
    if (filters.priority.length > 0 && !filters.priority.includes(todo.priority)) {
      return false;
    }
    
    // Category filter
    if (filters.categoryId.length > 0) {
      if (!todo.category_id || !filters.categoryId.includes(todo.category_id)) {
        return false;
      }
    }
    
    // Tags filter
    if (filters.tags.length > 0) {
      const hasMatchingTag = filters.tags.some(tag => todo.tags.includes(tag));
      if (!hasMatchingTag) {
        return false;
      }
    }
    
    // Date range filter
    if (filters.dueDateRange.start || filters.dueDateRange.end) {
      if (!todo.due_date) return false;
      const dueDate = new Date(todo.due_date);
      if (filters.dueDateRange.start && dueDate < filters.dueDateRange.start) return false;
      if (filters.dueDateRange.end && dueDate > filters.dueDateRange.end) return false;
    }
    
    // Search query filter
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      const searchableText = [
        todo.title,
        todo.description || '',
        ...todo.tags
      ].join(' ').toLowerCase();
      
      if (!searchableText.includes(query)) {
        return false;
      }
    }
    
    return true;
  });
};

const sortTodos = (todos: Todo[], sortBy: TodoFilters['sortBy'], sortOrder: TodoFilters['sortOrder']): Todo[] => {
  const sorted = [...todos].sort((a, b) => {
    let aValue: any;
    let bValue: any;
    
    switch (sortBy) {
      case 'position':
        aValue = a.position;
        bValue = b.position;
        break;
      case 'created_at':
        aValue = new Date(a.created_at);
        bValue = new Date(b.created_at);
        break;
      case 'updated_at':
        aValue = new Date(a.updated_at);
        bValue = new Date(b.updated_at);
        break;
      case 'due_date':
        aValue = a.due_date ? new Date(a.due_date) : new Date('9999-12-31');
        bValue = b.due_date ? new Date(b.due_date) : new Date('9999-12-31');
        break;
      case 'priority':
        const priorityOrder = { very_low: 1, low: 2, medium: 3, high: 4, very_high: 5 };
        aValue = priorityOrder[a.priority];
        bValue = priorityOrder[b.priority];
        break;
      case 'title':
        aValue = a.title.toLowerCase();
        bValue = b.title.toLowerCase();
        break;
      default:
        return 0;
    }
    
    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });
  
  return sorted;
};

// Create the store
export const useTodoStore = create<TodoState>()(
  subscribeWithSelector(
    immer(
      persist(
        (set, get) => ({
          // Initial state
          todos: [],
          categories: [],
          filters: defaultFilters,
          isLoading: false,
          error: null,
          selectedTodos: [],
          draggedTodo: null,
          totalCount: 0,
          completedCount: 0,
          pendingCount: 0,

          // Todo Management Actions
          loadTodos: async () => {
            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              // Get session from auth store or session manager
              const sessionId = 'current-session'; // TODO: Get from auth store
              const result = await todoService.getAll(sessionId);
              
              set((state) => {
                state.todos = result.todos || [];
                state.totalCount = result.todos?.length || 0;
                state.completedCount = result.todos?.filter(t => t.status === 'completed').length || 0;
                state.pendingCount = result.todos?.filter(t => t.status === 'pending').length || 0;
                state.isLoading = false;
              });
            } catch (error) {
              set((state) => {
                state.error = error instanceof Error ? error.message : 'Failed to load todos';
                state.isLoading = false;
              });
            }
          },

          createTodo: async (todoData) => {
            const tempId = generateTempId();
            const optimisticTodo: Todo = {
              id: tempId,
              ...todoData,
              created_at: new Date(),
              updated_at: new Date(),
              is_deleted: false,
            };

            // Optimistic update
            set((state) => {
              state.todos.unshift(optimisticTodo);
              state.totalCount += 1;
              if (optimisticTodo.status === 'pending') {
                state.pendingCount += 1;
              }
            });

            try {
              const sessionId = 'current-session'; // TODO: Get from auth store
              const result = await todoService.create(sessionId, todoData);
              
              // Replace optimistic todo with real one
              set((state) => {
                const index = state.todos.findIndex(t => t.id === tempId);
                if (index !== -1) {
                  state.todos[index] = result.todo;
                }
              });

              return result.todo.id;
            } catch (error) {
              // Revert optimistic update
              set((state) => {
                state.todos = state.todos.filter(t => t.id !== tempId);
                state.totalCount -= 1;
                if (optimisticTodo.status === 'pending') {
                  state.pendingCount -= 1;
                }
                state.error = error instanceof Error ? error.message : 'Failed to create todo';
              });
              throw error;
            }
          },

          updateTodo: async (id, updates) => {
            const originalTodo = get().todos.find(t => t.id === id);
            if (!originalTodo) return;

            // Optimistic update
            set((state) => {
              const index = state.todos.findIndex(t => t.id === id);
              if (index !== -1) {
                state.todos[index] = { ...state.todos[index], ...updates, updated_at: new Date() };
              }
            });

            try {
              const sessionId = 'current-session'; // TODO: Get from auth store
              const result = await todoService.update(sessionId, id, updates);
              
              set((state) => {
                const index = state.todos.findIndex(t => t.id === id);
                if (index !== -1) {
                  state.todos[index] = result.todo;
                }
              });
            } catch (error) {
              // Revert optimistic update
              set((state) => {
                const index = state.todos.findIndex(t => t.id === id);
                if (index !== -1) {
                  state.todos[index] = originalTodo;
                }
                state.error = error instanceof Error ? error.message : 'Failed to update todo';
              });
              throw error;
            }
          },

          deleteTodo: async (id) => {
            const originalTodo = get().todos.find(t => t.id === id);
            if (!originalTodo) return;

            // Optimistic update
            set((state) => {
              state.todos = state.todos.filter(t => t.id !== id);
              state.totalCount -= 1;
              if (originalTodo.status === 'completed') {
                state.completedCount -= 1;
              } else if (originalTodo.status === 'pending') {
                state.pendingCount -= 1;
              }
            });

            try {
              const sessionId = 'current-session'; // TODO: Get from auth store
              await todoService.delete(sessionId, id);
            } catch (error) {
              // Revert optimistic update
              set((state) => {
                state.todos.push(originalTodo);
                state.totalCount += 1;
                if (originalTodo.status === 'completed') {
                  state.completedCount += 1;
                } else if (originalTodo.status === 'pending') {
                  state.pendingCount += 1;
                }
                state.error = error instanceof Error ? error.message : 'Failed to delete todo';
              });
              throw error;
            }
          },

          toggleTodoComplete: async (id) => {
            const todo = get().todos.find(t => t.id === id);
            if (!todo) return;

            const newStatus: TodoStatus = todo.status === 'completed' ? 'pending' : 'completed';
            const updates: Partial<Todo> = {
              status: newStatus,
              completed_at: newStatus === 'completed' ? new Date() : undefined,
            };

            await get().updateTodo(id, updates);
          },

          reorderTodos: async (sourceIndex, destinationIndex, categoryId) => {
            // TODO: Implement reordering logic
            console.log('Reorder todos:', { sourceIndex, destinationIndex, categoryId });
          },

          // Category Management Actions
          loadCategories: async () => {
            // TODO: Implement category loading
            console.log('Load categories');
          },

          createCategory: async (categoryData: CreateCategoryRequest) => {
            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const category = await categoryService.createCategory(categoryData);

              set((state) => {
                state.categories.push(category);
                state.isLoading = false;
              });

              return category.id;
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : 'Failed to create category';
              set((state) => {
                state.error = errorMessage;
                state.isLoading = false;
              });
              throw error;
            }
          },

          updateCategory: async (id: string, updates: UpdateCategoryRequest) => {
            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const updatedCategory = await categoryService.updateCategory(id, updates);

              set((state) => {
                const index = state.categories.findIndex(cat => cat.id === id);
                if (index !== -1) {
                  state.categories[index] = updatedCategory;
                }
                state.isLoading = false;
              });
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : 'Failed to update category';
              set((state) => {
                state.error = errorMessage;
                state.isLoading = false;
              });
              throw error;
            }
          },

          deleteCategory: async (id: string) => {
            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              await categoryService.deleteCategory(id);

              set((state) => {
                state.categories = state.categories.filter(cat => cat.id !== id);
                // Also remove category from todos
                state.todos.forEach(todo => {
                  if (todo.category_id === id) {
                    todo.category_id = undefined;
                  }
                });
                state.isLoading = false;
              });
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : 'Failed to delete category';
              set((state) => {
                state.error = errorMessage;
                state.isLoading = false;
              });
              throw error;
            }
          },

          loadCategories: async () => {
            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const categories = await categoryService.getAllCategories();

              set((state) => {
                state.categories = categories;
                state.isLoading = false;
              });
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : 'Failed to load categories';
              set((state) => {
                state.error = errorMessage;
                state.isLoading = false;
              });
              throw error;
            }
          },

          // Filter Management Actions
          setFilters: (newFilters) => {
            set((state) => {
              state.filters = { ...state.filters, ...newFilters };
            });
          },

          clearFilters: () => {
            set((state) => {
              state.filters = defaultFilters;
            });
          },

          setSearchQuery: (query) => {
            set((state) => {
              state.filters.searchQuery = query;
            });
          },

          setSortConfig: (sortBy, sortOrder) => {
            set((state) => {
              state.filters.sortBy = sortBy;
              state.filters.sortOrder = sortOrder;
            });
          },

          // Tag Management Actions
          getAllTags: () => {
            const { todos } = get();
            const tagSet = new Set<string>();
            todos.forEach(todo => {
              todo.tags.forEach(tag => tagSet.add(tag));
            });
            return Array.from(tagSet).sort();
          },

          bulkUpdateTags: async (oldTag: string, newTag: string) => {
            const { todos } = get();
            const todosToUpdate = todos.filter(todo => todo.tags.includes(oldTag));

            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              // Update todos in parallel
              await Promise.all(
                todosToUpdate.map(todo => {
                  const updatedTags = todo.tags.map(tag => tag === oldTag ? newTag : tag);
                  return todoService.updateTodo(todo.id, { tags: updatedTags });
                })
              );

              // Update local state
              set((state) => {
                state.todos.forEach(todo => {
                  if (todo.tags.includes(oldTag)) {
                    todo.tags = todo.tags.map(tag => tag === oldTag ? newTag : tag);
                  }
                });
                state.isLoading = false;
              });
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : 'Failed to update tags';
              set((state) => {
                state.error = errorMessage;
                state.isLoading = false;
              });
              throw error;
            }
          },

          deleteTag: async (tag: string) => {
            const { todos } = get();
            const todosToUpdate = todos.filter(todo => todo.tags.includes(tag));

            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              // Remove tag from todos in parallel
              await Promise.all(
                todosToUpdate.map(todo => {
                  const updatedTags = todo.tags.filter(t => t !== tag);
                  return todoService.updateTodo(todo.id, { tags: updatedTags });
                })
              );

              // Update local state
              set((state) => {
                state.todos.forEach(todo => {
                  todo.tags = todo.tags.filter(t => t !== tag);
                });
                // Also remove from filters if present
                state.filters.tags = state.filters.tags.filter(t => t !== tag);
                state.isLoading = false;
              });
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : 'Failed to delete tag';
              set((state) => {
                state.error = errorMessage;
                state.isLoading = false;
              });
              throw error;
            }
          },

          // Selection Management Actions
          selectTodo: (id) => {
            set((state) => {
              if (!state.selectedTodos.includes(id)) {
                state.selectedTodos.push(id);
              }
            });
          },

          deselectTodo: (id) => {
            set((state) => {
              state.selectedTodos = state.selectedTodos.filter(todoId => todoId !== id);
            });
          },

          selectAllTodos: () => {
            set((state) => {
              const filteredTodos = get().getFilteredTodos();
              state.selectedTodos = filteredTodos.map(todo => todo.id);
            });
          },

          clearSelection: () => {
            set((state) => {
              state.selectedTodos = [];
            });
          },

          toggleTodoSelection: (id) => {
            const { selectedTodos } = get();
            if (selectedTodos.includes(id)) {
              get().deselectTodo(id);
            } else {
              get().selectTodo(id);
            }
          },

          // Drag and Drop Actions
          setDraggedTodo: (id) => {
            set((state) => {
              state.draggedTodo = id;
            });
          },

          // Computed selectors
          getFilteredTodos: () => {
            const { todos, filters } = get();
            const filtered = filterTodos(todos, filters);
            return sortTodos(filtered, filters.sortBy, filters.sortOrder);
          },

          getTodosByCategory: (categoryId) => {
            const { todos } = get();
            return todos.filter(todo => todo.category_id === categoryId);
          },

          getCompletedTodos: () => {
            const { todos } = get();
            return todos.filter(todo => todo.status === 'completed');
          },

          getPendingTodos: () => {
            const { todos } = get();
            return todos.filter(todo => todo.status === 'pending');
          },

          getOverdueTodos: () => {
            const { todos } = get();
            const now = new Date();
            return todos.filter(todo => 
              todo.due_date && 
              new Date(todo.due_date) < now && 
              todo.status !== 'completed'
            );
          },

          getTodoStats: () => {
            const { todos } = get();
            const now = new Date();
            const overdue = todos.filter(todo => 
              todo.due_date && 
              new Date(todo.due_date) < now && 
              todo.status !== 'completed'
            ).length;

            return {
              total: todos.length,
              completed: todos.filter(t => t.status === 'completed').length,
              pending: todos.filter(t => t.status === 'pending').length,
              overdue,
            };
          },
        }),
        {
          name: 'todo-store',
          partialize: (state) => ({
            filters: state.filters,
            selectedTodos: state.selectedTodos,
          }),
        }
      )
    )
  )
);
