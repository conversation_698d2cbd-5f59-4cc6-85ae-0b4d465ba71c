import { renderHook, act } from '@testing-library/react';
import { useTodoStore, useAuthStore, useUIStore } from '../index';

// Mock the services
jest.mock('@renderer/services/todo.service', () => ({
  todoService: {
    getAll: jest.fn().mockResolvedValue({ todos: [] }),
    create: jest.fn().mockResolvedValue({ todo: { id: 'test-id' } }),
    update: jest.fn().mockResolvedValue({ todo: { id: 'test-id' } }),
    delete: jest.fn().mockResolvedValue({}),
  },
}));

jest.mock('@renderer/services/auth.service', () => ({
  authService: {
    login: jest.fn().mockResolvedValue({ 
      user: { id: 'user-1', username: 'testuser' },
      session: { session_id: 'session-1', expires_at: new Date(Date.now() + 3600000) }
    }),
    logout: jest.fn().mockResolvedValue({}),
  },
}));

describe('Zustand Stores', () => {
  beforeEach(() => {
    // Reset stores before each test
    useTodoStore.getState().todos = [];
    useAuthStore.getState().user = null;
    useUIStore.getState().theme = 'frutiger-aero';
  });

  describe('TodoStore', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useTodoStore());
      
      expect(result.current.todos).toEqual([]);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
      expect(result.current.filters.searchQuery).toBe('');
    });

    it('should update search query', () => {
      const { result } = renderHook(() => useTodoStore());
      
      act(() => {
        result.current.setSearchQuery('test query');
      });
      
      expect(result.current.filters.searchQuery).toBe('test query');
    });

    it('should manage todo selection', () => {
      const { result } = renderHook(() => useTodoStore());
      
      act(() => {
        result.current.selectTodo('todo-1');
        result.current.selectTodo('todo-2');
      });
      
      expect(result.current.selectedTodos).toEqual(['todo-1', 'todo-2']);
      
      act(() => {
        result.current.deselectTodo('todo-1');
      });
      
      expect(result.current.selectedTodos).toEqual(['todo-2']);
      
      act(() => {
        result.current.clearSelection();
      });
      
      expect(result.current.selectedTodos).toEqual([]);
    });
  });

  describe('AuthStore', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useAuthStore());
      
      expect(result.current.user).toBe(null);
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.isLoading).toBe(false);
    });

    it('should handle login', async () => {
      const { result } = renderHook(() => useAuthStore());
      
      await act(async () => {
        await result.current.login('testuser', 'password');
      });
      
      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.user?.username).toBe('testuser');
    });

    it('should handle logout', async () => {
      const { result } = renderHook(() => useAuthStore());
      
      // First login
      await act(async () => {
        await result.current.login('testuser', 'password');
      });
      
      expect(result.current.isAuthenticated).toBe(true);
      
      // Then logout
      await act(async () => {
        await result.current.logout();
      });
      
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.user).toBe(null);
    });
  });

  describe('UIStore', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useUIStore());
      
      expect(result.current.theme).toBe('frutiger-aero');
      expect(result.current.sidebarOpen).toBe(true);
      expect(result.current.activeModal).toBe(null);
    });

    it('should handle theme changes', () => {
      const { result } = renderHook(() => useUIStore());
      
      act(() => {
        result.current.setTheme('dark');
      });
      
      expect(result.current.theme).toBe('dark');
    });

    it('should handle modal management', () => {
      const { result } = renderHook(() => useUIStore());
      
      act(() => {
        result.current.openModal('todo-form', { id: 'test' });
      });
      
      expect(result.current.activeModal).toBe('todo-form');
      expect(result.current.modalData).toEqual({ id: 'test' });
      
      act(() => {
        result.current.closeModal();
      });
      
      expect(result.current.activeModal).toBe(null);
      expect(result.current.modalData).toBe(null);
    });

    it('should handle notifications', () => {
      const { result } = renderHook(() => useUIStore());
      
      let notificationId: string;
      
      act(() => {
        notificationId = result.current.addNotification({
          type: 'success',
          title: 'Test',
          message: 'Test message'
        });
      });
      
      expect(result.current.notifications).toHaveLength(1);
      expect(result.current.notifications[0].title).toBe('Test');
      
      act(() => {
        result.current.removeNotification(notificationId);
      });
      
      expect(result.current.notifications).toHaveLength(0);
    });

    it('should handle sidebar state', () => {
      const { result } = renderHook(() => useUIStore());
      
      expect(result.current.sidebarOpen).toBe(true);
      
      act(() => {
        result.current.toggleSidebar();
      });
      
      expect(result.current.sidebarOpen).toBe(false);
      
      act(() => {
        result.current.setSidebarOpen(true);
      });
      
      expect(result.current.sidebarOpen).toBe(true);
    });
  });
});
