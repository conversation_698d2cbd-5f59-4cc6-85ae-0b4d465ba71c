// Export all stores
export { useTodoStore } from './todoStore';
export type { TodoState, TodoFilters } from './todoStore';

export { useAuthStore, useSessionValidation } from './authStore';
export type { AuthState, RegisterData } from './authStore';

export { useUIStore, useNotifications, initializeTheme } from './uiStore';
export type { 
  UIState, 
  Theme, 
  ViewMode, 
  CurrentView, 
  ModalType, 
  Notification, 
  NotificationAction 
} from './uiStore';

// Store initialization helper
export const initializeStores = () => {
  // Initialize theme from localStorage
  initializeTheme();
  
  // You can add other store initialization logic here
  console.log('Stores initialized');
};

// Combined store hooks for common use cases
export const useAppState = () => {
  const todoStore = useTodoStore();
  const authStore = useAuthStore();
  const uiStore = useUIStore();
  
  return {
    todos: todoStore,
    auth: authStore,
    ui: uiStore,
  };
};

// Selector hooks for performance optimization
export const useTodoCount = () => useTodoStore(state => state.totalCount);
export const useCompletedCount = () => useTodoStore(state => state.completedCount);
export const usePendingCount = () => useTodoStore(state => state.pendingCount);
export const useFilteredTodos = () => useTodoStore(state => state.getFilteredTodos());
export const useTodoStats = () => useTodoStore(state => state.getTodoStats());

export const useCurrentUser = () => useAuthStore(state => state.user);
export const useIsAuthenticated = () => useAuthStore(state => state.isAuthenticated);
export const useUserDisplayName = () => useAuthStore(state => state.getUserDisplayName());

export const useCurrentTheme = () => useUIStore(state => state.theme);
export const useCurrentView = () => useUIStore(state => state.currentView);
export const useActiveModal = () => useUIStore(state => state.activeModal);
export const useGlobalLoading = () => useUIStore(state => state.globalLoading);
