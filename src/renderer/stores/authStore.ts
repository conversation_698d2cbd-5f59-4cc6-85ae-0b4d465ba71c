import React from 'react';
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { persist } from 'zustand/middleware';
import { User, UserProfile, UserSession } from '@shared/types';
import { authService } from '@renderer/services/auth.service';

// Auth state interface
export interface AuthState {
  // User data
  user: User | null;
  profile: UserProfile | null;
  session: UserSession | null;
  
  // Authentication status
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Session management
  sessionId: string | null;
  sessionExpiry: Date | null;
  lastActivity: Date | null;
  
  // Actions - Authentication
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  refreshSession: () => Promise<void>;
  
  // Actions - Profile Management
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  loadProfile: () => Promise<void>;
  
  // Actions - Session Management
  validateSession: () => Promise<boolean>;
  extendSession: () => Promise<void>;
  clearSession: () => void;
  
  // Actions - Error Management
  clearError: () => void;
  setError: (error: string) => void;
  
  // Computed selectors
  isSessionValid: () => boolean;
  getSessionTimeRemaining: () => number;
  getUserDisplayName: () => string;
}

// Registration data interface
export interface RegisterData {
  username: string;
  password: string;
  email?: string;
  fullName?: string;
}

// Helper functions
const isSessionExpired = (expiry: Date | null): boolean => {
  if (!expiry) return true;
  return new Date() >= expiry;
};

const getTimeRemaining = (expiry: Date | null): number => {
  if (!expiry) return 0;
  const now = new Date();
  const remaining = expiry.getTime() - now.getTime();
  return Math.max(0, remaining);
};

// Create the auth store
export const useAuthStore = create<AuthState>()(
  subscribeWithSelector(
    immer(
      persist(
        (set, get) => ({
          // Initial state
          user: null,
          profile: null,
          session: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
          sessionId: null,
          sessionExpiry: null,
          lastActivity: null,

          // Authentication Actions
          login: async (username, password) => {
            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const result = await authService.login(username, password);
              
              set((state) => {
                state.user = result.user;
                state.session = result.session;
                state.sessionId = result.session.session_id;
                state.sessionExpiry = new Date(result.session.expires_at);
                state.lastActivity = new Date();
                state.isAuthenticated = true;
                state.isLoading = false;
              });

              // Load user profile after successful login
              await get().loadProfile();
            } catch (error) {
              set((state) => {
                state.error = error instanceof Error ? error.message : 'Login failed';
                state.isLoading = false;
                state.isAuthenticated = false;
              });
              throw error;
            }
          },

          logout: async () => {
            set((state) => {
              state.isLoading = true;
            });

            try {
              const { sessionId } = get();
              if (sessionId) {
                await authService.logout(sessionId);
              }
            } catch (error) {
              console.error('Logout error:', error);
              // Continue with local logout even if server logout fails
            } finally {
              set((state) => {
                state.user = null;
                state.profile = null;
                state.session = null;
                state.sessionId = null;
                state.sessionExpiry = null;
                state.lastActivity = null;
                state.isAuthenticated = false;
                state.isLoading = false;
                state.error = null;
              });
            }
          },

          register: async (userData) => {
            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const result = await authService.register(userData);
              
              set((state) => {
                state.user = result.user;
                state.session = result.session;
                state.sessionId = result.session.session_id;
                state.sessionExpiry = new Date(result.session.expires_at);
                state.lastActivity = new Date();
                state.isAuthenticated = true;
                state.isLoading = false;
              });

              // Load user profile after successful registration
              await get().loadProfile();
            } catch (error) {
              set((state) => {
                state.error = error instanceof Error ? error.message : 'Registration failed';
                state.isLoading = false;
                state.isAuthenticated = false;
              });
              throw error;
            }
          },

          refreshSession: async () => {
            const { sessionId } = get();
            if (!sessionId) {
              throw new Error('No active session to refresh');
            }

            set((state) => {
              state.isLoading = true;
            });

            try {
              const result = await authService.refreshSession(sessionId);
              
              set((state) => {
                state.session = result.session;
                state.sessionExpiry = new Date(result.session.expires_at);
                state.lastActivity = new Date();
                state.isLoading = false;
              });
            } catch (error) {
              set((state) => {
                state.error = error instanceof Error ? error.message : 'Session refresh failed';
                state.isLoading = false;
              });
              
              // If refresh fails, logout the user
              await get().logout();
              throw error;
            }
          },

          // Profile Management Actions
          updateProfile: async (updates) => {
            const { user } = get();
            if (!user) {
              throw new Error('No authenticated user');
            }

            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const result = await authService.updateProfile(user.id, updates);
              
              set((state) => {
                state.profile = result.profile;
                state.isLoading = false;
              });
            } catch (error) {
              set((state) => {
                state.error = error instanceof Error ? error.message : 'Profile update failed';
                state.isLoading = false;
              });
              throw error;
            }
          },

          loadProfile: async () => {
            const { user } = get();
            if (!user) {
              return;
            }

            try {
              const profile = await authService.getProfile(user.id);
              
              set((state) => {
                state.profile = profile;
              });
            } catch (error) {
              console.error('Failed to load profile:', error);
              // Don't set error state for profile loading failures
            }
          },

          // Session Management Actions
          validateSession: async () => {
            const { sessionId, sessionExpiry } = get();
            
            if (!sessionId || !sessionExpiry) {
              return false;
            }

            // Check if session is expired locally
            if (isSessionExpired(sessionExpiry)) {
              await get().logout();
              return false;
            }

            try {
              const isValid = await authService.validateSession(sessionId);
              
              if (!isValid) {
                await get().logout();
                return false;
              }

              // Update last activity
              set((state) => {
                state.lastActivity = new Date();
              });

              return true;
            } catch (error) {
              console.error('Session validation error:', error);
              await get().logout();
              return false;
            }
          },

          extendSession: async () => {
            const { sessionId } = get();
            if (!sessionId) {
              return;
            }

            try {
              const result = await authService.extendSession(sessionId);
              
              set((state) => {
                state.sessionExpiry = new Date(result.expiresAt);
                state.lastActivity = new Date();
              });
            } catch (error) {
              console.error('Failed to extend session:', error);
              // Don't throw error, just log it
            }
          },

          clearSession: () => {
            set((state) => {
              state.user = null;
              state.profile = null;
              state.session = null;
              state.sessionId = null;
              state.sessionExpiry = null;
              state.lastActivity = null;
              state.isAuthenticated = false;
              state.error = null;
            });
          },

          // Error Management Actions
          clearError: () => {
            set((state) => {
              state.error = null;
            });
          },

          setError: (error) => {
            set((state) => {
              state.error = error;
            });
          },

          // Computed selectors
          isSessionValid: () => {
            const { sessionExpiry } = get();
            return !isSessionExpired(sessionExpiry);
          },

          getSessionTimeRemaining: () => {
            const { sessionExpiry } = get();
            return getTimeRemaining(sessionExpiry);
          },

          getUserDisplayName: () => {
            const { user, profile } = get();
            if (profile?.full_name) {
              return profile.full_name;
            }
            if (user?.username) {
              return user.username;
            }
            return 'User';
          },
        }),
        {
          name: 'auth-store',
          partialize: (state) => ({
            user: state.user,
            profile: state.profile,
            session: state.session,
            sessionId: state.sessionId,
            sessionExpiry: state.sessionExpiry,
            lastActivity: state.lastActivity,
            isAuthenticated: state.isAuthenticated,
          }),
        }
      )
    )
  )
);

// Session validation hook for automatic session management
export const useSessionValidation = () => {
  const validateSession = useAuthStore(state => state.validateSession);
  const extendSession = useAuthStore(state => state.extendSession);
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);
  const sessionExpiry = useAuthStore(state => state.sessionExpiry);

  // Auto-validate session on mount and periodically
  React.useEffect(() => {
    if (!isAuthenticated) return;

    const validateAndExtend = async () => {
      const isValid = await validateSession();
      if (isValid) {
        // Extend session if it's close to expiring (within 5 minutes)
        const timeRemaining = getTimeRemaining(sessionExpiry);
        if (timeRemaining < 5 * 60 * 1000) { // 5 minutes in milliseconds
          await extendSession();
        }
      }
    };

    // Validate immediately
    validateAndExtend();

    // Set up periodic validation (every 5 minutes)
    const interval = setInterval(validateAndExtend, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [isAuthenticated, validateSession, extendSession, sessionExpiry]);
};
