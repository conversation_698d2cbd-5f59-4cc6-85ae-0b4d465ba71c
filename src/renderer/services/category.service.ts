import { Category } from '@shared/types';

declare global {
  interface Window {
    electronAPI: {
      categories: {
        getAll: (sessionId: string) => Promise<any>;
        create: (sessionId: string, categoryData: any) => Promise<any>;
        update: (sessionId: string, categoryId: string, updates: any) => Promise<any>;
        delete: (sessionId: string, categoryId: string) => Promise<any>;
        reorder: (sessionId: string, categoryOrders: any[]) => Promise<any>;
      };
    };
  }
}

export interface CreateCategoryRequest {
  name: string;
  color?: string;
  icon?: string;
  is_default?: boolean;
}

export interface UpdateCategoryRequest {
  name?: string;
  color?: string;
  icon?: string;
  is_default?: boolean;
}

export interface CategoryOrderRequest {
  id: string;
  sort_order: number;
}

export interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}

export class CategoryService {
  private sessionId: string | null = null;

  constructor() {
    // For now, we'll use a mock session ID
    // In a real implementation, this would come from authentication
    this.sessionId = 'mock-session-id';
  }

  setSessionId(sessionId: string): void {
    this.sessionId = sessionId;
  }

  private async makeRequest<T>(
    operation: string,
    ...args: any[]
  ): Promise<T> {
    if (!this.sessionId) {
      throw new Error('No active session');
    }

    if (!window.electronAPI?.categories) {
      throw new Error('Electron API not available');
    }

    const response: APIResponse<T> = await (window.electronAPI.categories as any)[operation](
      this.sessionId,
      ...args
    );

    if (!response.success) {
      throw new Error(response.error || 'Unknown error');
    }

    return response.data as T;
  }

  async getAllCategories(): Promise<Category[]> {
    return this.makeRequest('getAll');
  }

  async createCategory(categoryData: CreateCategoryRequest): Promise<Category> {
    return this.makeRequest('create', categoryData);
  }

  async updateCategory(categoryId: string, updates: UpdateCategoryRequest): Promise<Category> {
    return this.makeRequest('update', categoryId, updates);
  }

  async deleteCategory(categoryId: string): Promise<{ success: boolean }> {
    return this.makeRequest('delete', categoryId);
  }

  async reorderCategories(categoryOrders: CategoryOrderRequest[]): Promise<Category[]> {
    return this.makeRequest('reorder', categoryOrders);
  }
}

// Export a singleton instance
export const categoryService = new CategoryService();
