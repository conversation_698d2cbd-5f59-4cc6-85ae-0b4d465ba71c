import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Edit3,
  Trash2,
  Star,
  MoreVertical,
  Plus,
  Folder,
  Tag,
  Heart,
  Zap,
  Home,
  Briefcase,
  ShoppingCart,
  Calendar,
  Book,
  Music,
  Camera,
  Coffee
} from 'lucide-react';
import { Category } from '@shared/types';
import { ConfirmationDialog } from '@renderer/components/ui/ConfirmationDialog';

interface CategoryListProps {
  categories: Category[];
  onEdit: (category: Category) => void;
  onDelete: (categoryId: string) => void;
  onCreate: () => void;
  isLoading?: boolean;
}

interface CategoryItemProps {
  category: Category;
  onEdit: (category: Category) => void;
  onDelete: (categoryId: string) => void;
  todoCount?: number;
}

const ICON_MAP: Record<string, React.ComponentType<any>> = {
  folder: Folder,
  tag: Tag,
  star: Star,
  heart: Heart,
  zap: Zap,
  home: Home,
  briefcase: Briefcase,
  'shopping-cart': ShoppingCart,
  calendar: Calendar,
  book: Book,
  music: Music,
  camera: Camera,
  coffee: Coffee,
};

const CategoryItem: React.FC<CategoryItemProps> = ({
  category,
  onEdit,
  onDelete,
  todoCount = 0
}) => {
  const [showMenu, setShowMenu] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const getCategoryIcon = (iconName?: string) => {
    const IconComponent = iconName ? ICON_MAP[iconName] : Folder;
    return IconComponent || Folder;
  };

  const handleEdit = () => {
    onEdit(category);
    setShowMenu(false);
  };

  const handleDelete = () => {
    setShowDeleteDialog(true);
    setShowMenu(false);
  };

  const confirmDelete = () => {
    onDelete(category.id);
    setShowDeleteDialog(false);
  };

  const IconComponent = getCategoryIcon(category.icon);

  return (
    <>
      <motion.div
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className="fa-glass-panel p-4 hover:shadow-lg transition-all duration-200"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 flex-1">
            {/* Category Icon */}
            <div
              className="w-10 h-10 rounded-lg flex items-center justify-center"
              style={{ backgroundColor: category.color }}
            >
              <IconComponent className="w-5 h-5 text-white" />
            </div>

            {/* Category Info */}
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h3 className="fa-body font-semibold text-fa-gray-800">
                  {category.name}
                </h3>
                {category.is_default && (
                  <Star className="w-4 h-4 text-fa-yellow-500 fill-current" />
                )}
              </div>
              <p className="fa-caption text-fa-gray-500">
                {todoCount} {todoCount === 1 ? 'todo' : 'todos'}
              </p>
            </div>
          </div>

          {/* Actions Menu */}
          <div className="relative">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setShowMenu(!showMenu)}
              className="p-2 rounded-lg text-fa-gray-400 hover:text-fa-gray-600 hover:bg-fa-white-glass"
            >
              <MoreVertical className="w-4 h-4" />
            </motion.button>

            <AnimatePresence>
              {showMenu && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, y: -10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: -10 }}
                  className="absolute right-0 top-full mt-2 bg-white border border-fa-gray-200 rounded-lg shadow-lg z-10 min-w-[120px]"
                >
                  <motion.button
                    whileHover={{ backgroundColor: 'rgba(0, 0, 0, 0.05)' }}
                    onClick={handleEdit}
                    className="w-full flex items-center space-x-2 px-3 py-2 text-left text-fa-gray-700 hover:bg-fa-gray-50 rounded-t-lg"
                  >
                    <Edit3 className="w-4 h-4" />
                    <span>Edit</span>
                  </motion.button>
                  <motion.button
                    whileHover={{ backgroundColor: 'rgba(220, 53, 69, 0.05)' }}
                    onClick={handleDelete}
                    className="w-full flex items-center space-x-2 px-3 py-2 text-left text-fa-error hover:bg-fa-error hover:bg-opacity-5 rounded-b-lg"
                  >
                    <Trash2 className="w-4 h-4" />
                    <span>Delete</span>
                  </motion.button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </motion.div>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={confirmDelete}
        title="Delete Category"
        message={`Are you sure you want to delete "${category.name}"? This action cannot be undone. Todos in this category will be moved to "No Category".`}
        confirmText="Delete"
        confirmVariant="danger"
      />
    </>
  );
};

export const CategoryList: React.FC<CategoryListProps> = ({
  categories,
  onEdit,
  onDelete,
  onCreate,
  isLoading = false
}) => {
  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, index) => (
          <div key={index} className="fa-glass-panel p-4 animate-pulse">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-fa-gray-200 rounded-lg" />
              <div className="flex-1">
                <div className="h-4 bg-fa-gray-200 rounded w-24 mb-2" />
                <div className="h-3 bg-fa-gray-200 rounded w-16" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (categories.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="fa-glass-panel p-8">
          <Folder className="w-16 h-16 text-fa-gray-300 mx-auto mb-4" />
          <h3 className="fa-h4 text-fa-gray-600 mb-2">No Categories Yet</h3>
          <p className="fa-body text-fa-gray-500 mb-6">
            Create your first category to organize your todos
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onCreate}
            className="fa-button-primary"
          >
            <Plus className="w-4 h-4 mr-2" />
            Create Category
          </motion.button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="fa-h3 text-fa-gray-800">Categories</h2>
          <p className="fa-body text-fa-gray-500">
            {categories.length} {categories.length === 1 ? 'category' : 'categories'}
          </p>
        </div>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onCreate}
          className="fa-button-primary"
        >
          <Plus className="w-4 h-4 mr-2" />
          New Category
        </motion.button>
      </div>

      {/* Category List */}
      <div className="space-y-3">
        <AnimatePresence>
          {categories.map((category) => (
            <CategoryItem
              key={category.id}
              category={category}
              onEdit={onEdit}
              onDelete={onDelete}
              todoCount={0} // TODO: Calculate actual todo count
            />
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
};
