import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChevronDown,
  Search,
  Plus,
  Folder,
  Tag,
  Star,
  Heart,
  Zap,
  Home,
  Briefcase,
  ShoppingCart,
  Calendar,
  Book,
  Music,
  Camera,
  Coffee,
  X
} from 'lucide-react';
import { Category } from '@shared/types';

interface CategorySelectorProps {
  categories: Category[];
  selectedCategoryId?: string;
  onSelect: (categoryId: string | undefined) => void;
  onCreateNew?: () => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

const ICON_MAP: Record<string, React.ComponentType<any>> = {
  folder: Folder,
  tag: Tag,
  star: Star,
  heart: Heart,
  zap: Zap,
  home: Home,
  briefcase: Briefcase,
  'shopping-cart': ShoppingCart,
  calendar: Calendar,
  book: Book,
  music: Music,
  camera: Camera,
  coffee: Coffee,
};

export const CategorySelector: React.FC<CategorySelectorProps> = ({
  categories,
  selectedCategoryId,
  onSelect,
  onCreateNew,
  placeholder = "Select a category...",
  disabled = false,
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const selectedCategory = categories.find(cat => cat.id === selectedCategoryId);

  // Filter categories based on search query
  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchQuery('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      setSearchQuery('');
    }
  };

  const handleSelect = (categoryId: string | undefined) => {
    onSelect(categoryId);
    setIsOpen(false);
    setSearchQuery('');
  };

  const handleCreateNew = () => {
    if (onCreateNew) {
      onCreateNew();
      setIsOpen(false);
      setSearchQuery('');
    }
  };

  const getCategoryIcon = (iconName?: string) => {
    const IconComponent = iconName ? ICON_MAP[iconName] : Folder;
    return IconComponent || Folder;
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Trigger Button */}
      <motion.button
        type="button"
        whileHover={!disabled ? { scale: 1.01 } : {}}
        whileTap={!disabled ? { scale: 0.99 } : {}}
        onClick={handleToggle}
        disabled={disabled}
        className={`w-full flex items-center justify-between p-3 border rounded-lg transition-all duration-200 ${
          disabled
            ? 'bg-fa-gray-100 border-fa-gray-200 text-fa-gray-400 cursor-not-allowed'
            : isOpen
            ? 'border-fa-blue-400 bg-fa-blue-50'
            : 'border-fa-gray-300 bg-white hover:border-fa-gray-400'
        }`}
      >
        <div className="flex items-center space-x-3">
          {selectedCategory ? (
            <>
              <div
                className="w-5 h-5 rounded flex items-center justify-center"
                style={{ backgroundColor: selectedCategory.color }}
              >
                {React.createElement(getCategoryIcon(selectedCategory.icon), {
                  className: "w-3 h-3 text-white"
                })}
              </div>
              <span className="text-fa-gray-800">{selectedCategory.name}</span>
            </>
          ) : (
            <>
              <Folder className="w-5 h-5 text-fa-gray-400" />
              <span className="text-fa-gray-500">{placeholder}</span>
            </>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {selectedCategory && (
            <motion.button
              type="button"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={(e) => {
                e.stopPropagation();
                handleSelect(undefined);
              }}
              className="p-1 rounded text-fa-gray-400 hover:text-fa-gray-600"
            >
              <X className="w-3 h-3" />
            </motion.button>
          )}
          <ChevronDown
            className={`w-4 h-4 text-fa-gray-400 transition-transform duration-200 ${
              isOpen ? 'rotate-180' : ''
            }`}
          />
        </div>
      </motion.button>

      {/* Dropdown */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white border border-fa-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-hidden"
          >
            {/* Search Input */}
            <div className="p-3 border-b border-fa-gray-100">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-fa-gray-400" />
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search categories..."
                  className="w-full pl-10 pr-4 py-2 border border-fa-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-fa-blue-400 focus:border-transparent"
                />
              </div>
            </div>

            {/* Category List */}
            <div className="max-h-48 overflow-y-auto">
              {/* None Option */}
              <motion.button
                type="button"
                whileHover={{ backgroundColor: 'rgba(0, 0, 0, 0.05)' }}
                onClick={() => handleSelect(undefined)}
                className="w-full flex items-center space-x-3 p-3 text-left hover:bg-fa-gray-50 transition-colors duration-150"
              >
                <div className="w-5 h-5 rounded border-2 border-dashed border-fa-gray-300 flex items-center justify-center">
                  <X className="w-3 h-3 text-fa-gray-400" />
                </div>
                <span className="text-fa-gray-500 italic">No category</span>
              </motion.button>

              {/* Filtered Categories */}
              {filteredCategories.map((category) => {
                const IconComponent = getCategoryIcon(category.icon);
                return (
                  <motion.button
                    key={category.id}
                    type="button"
                    whileHover={{ backgroundColor: 'rgba(0, 0, 0, 0.05)' }}
                    onClick={() => handleSelect(category.id)}
                    className={`w-full flex items-center space-x-3 p-3 text-left transition-colors duration-150 ${
                      selectedCategoryId === category.id
                        ? 'bg-fa-blue-50 text-fa-blue-800'
                        : 'hover:bg-fa-gray-50'
                    }`}
                  >
                    <div
                      className="w-5 h-5 rounded flex items-center justify-center"
                      style={{ backgroundColor: category.color }}
                    >
                      <IconComponent className="w-3 h-3 text-white" />
                    </div>
                    <span className="flex-1">{category.name}</span>
                    {category.is_default && (
                      <Star className="w-3 h-3 text-fa-yellow-500" />
                    )}
                  </motion.button>
                );
              })}

              {/* No Results */}
              {searchQuery && filteredCategories.length === 0 && (
                <div className="p-3 text-center text-fa-gray-500 text-sm">
                  No categories found
                </div>
              )}

              {/* Create New Option */}
              {onCreateNew && (
                <motion.button
                  type="button"
                  whileHover={{ backgroundColor: 'rgba(59, 130, 246, 0.05)' }}
                  onClick={handleCreateNew}
                  className="w-full flex items-center space-x-3 p-3 text-left border-t border-fa-gray-100 text-fa-blue-600 hover:bg-fa-blue-50 transition-colors duration-150"
                >
                  <div className="w-5 h-5 rounded border-2 border-dashed border-fa-blue-300 flex items-center justify-center">
                    <Plus className="w-3 h-3" />
                  </div>
                  <span>Create new category</span>
                </motion.button>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
