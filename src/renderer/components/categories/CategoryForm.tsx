import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm } from 'react-hook-form';
import {
  X,
  Save,
  Palette,
  Tag,
  Star,
  Folder,
  Heart,
  Zap,
  Home,
  Briefcase,
  ShoppingCart,
  Calendar,
  Book,
  Music,
  Camera,
  Coffee
} from 'lucide-react';
import { Category } from '@shared/types';
import { CreateCategoryRequest, UpdateCategoryRequest } from '@renderer/services/category.service';

interface CategoryFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateCategoryRequest | UpdateCategoryRequest) => Promise<void>;
  category?: Category | null;
  mode: 'create' | 'edit';
}

interface CategoryFormData {
  name: string;
  color: string;
  icon: string;
  is_default: boolean;
}

const PREDEFINED_COLORS = [
  '#007bff', '#28a745', '#dc3545', '#ffc107', '#17a2b8',
  '#6f42c1', '#e83e8c', '#fd7e14', '#20c997', '#6c757d',
  '#343a40', '#f8f9fa', '#ff6b6b', '#4ecdc4', '#45b7d1',
  '#96ceb4', '#ffeaa7', '#dda0dd', '#98d8c8', '#f7dc6f'
];

const PREDEFINED_ICONS = [
  { icon: Folder, name: 'folder' },
  { icon: Tag, name: 'tag' },
  { icon: Star, name: 'star' },
  { icon: Heart, name: 'heart' },
  { icon: Zap, name: 'zap' },
  { icon: Home, name: 'home' },
  { icon: Briefcase, name: 'briefcase' },
  { icon: ShoppingCart, name: 'shopping-cart' },
  { icon: Calendar, name: 'calendar' },
  { icon: Book, name: 'book' },
  { icon: Music, name: 'music' },
  { icon: Camera, name: 'camera' },
  { icon: Coffee, name: 'coffee' }
];

export const CategoryForm: React.FC<CategoryFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  category,
  mode
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    control,
    watch,
    setValue,
    reset,
    formState: { errors, isValid }
  } = useForm<CategoryFormData>({
    defaultValues: {
      name: '',
      color: '#007bff',
      icon: 'folder',
      is_default: false,
    },
    mode: 'onChange'
  });

  const watchedColor = watch('color');
  const watchedIcon = watch('icon');

  // Reset form when modal opens/closes or category changes
  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && category) {
        reset({
          name: category.name,
          color: category.color,
          icon: category.icon || 'folder',
          is_default: category.is_default,
        });
      } else {
        // Reset to defaults for create mode
        reset({
          name: '',
          color: '#007bff',
          icon: 'folder',
          is_default: false,
        });
      }
      setError(null);
    }
  }, [isOpen, mode, category, reset]);

  const onFormSubmit = async (data: CategoryFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      await onSubmit({
        name: data.name.trim(),
        color: data.color,
        icon: data.icon,
        is_default: data.is_default,
      });

      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const getSelectedIcon = () => {
    const iconData = PREDEFINED_ICONS.find(i => i.name === watchedIcon);
    return iconData ? iconData.icon : Folder;
  };

  if (!isOpen) return null;

  const SelectedIcon = getSelectedIcon();

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="fa-glass-panel w-full max-w-md max-h-[90vh] overflow-y-auto"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-fa-white-glass">
            <div className="flex items-center space-x-3">
              <div 
                className="w-8 h-8 rounded-lg flex items-center justify-center"
                style={{ backgroundColor: watchedColor }}
              >
                <SelectedIcon className="w-4 h-4 text-white" />
              </div>
              <h2 className="fa-h3 text-fa-gray-800">
                {mode === 'create' ? 'Create Category' : 'Edit Category'}
              </h2>
            </div>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={onClose}
              className="p-2 rounded-lg text-fa-gray-500 hover:bg-fa-white-glass"
            >
              <X className="w-5 h-5" />
            </motion.button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit(onFormSubmit)} className="p-6 space-y-6">
            {/* Error Display */}
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-4 bg-fa-error bg-opacity-10 border border-fa-error rounded-lg"
              >
                <p className="text-fa-error text-sm">{error}</p>
              </motion.div>
            )}

            {/* Category Name */}
            <div>
              <label className="block text-sm font-medium text-fa-gray-700 mb-2">
                Category Name
              </label>
              <input
                {...register('name', {
                  required: 'Category name is required',
                  minLength: { value: 1, message: 'Name must be at least 1 character' },
                  maxLength: { value: 50, message: 'Name must be less than 50 characters' }
                })}
                className="fa-input w-full"
                placeholder="Enter category name..."
                autoFocus
              />
              {errors.name && (
                <p className="text-fa-error text-sm mt-1">{errors.name.message}</p>
              )}
            </div>

            {/* Color Picker */}
            <div>
              <label className="block text-sm font-medium text-fa-gray-700 mb-2">
                <Palette className="w-4 h-4 inline mr-1" />
                Color
              </label>
              <div className="grid grid-cols-10 gap-2">
                {PREDEFINED_COLORS.map((color) => (
                  <motion.button
                    key={color}
                    type="button"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setValue('color', color)}
                    className={`w-8 h-8 rounded-lg border-2 transition-all duration-200 ${
                      watchedColor === color
                        ? 'border-fa-gray-800 shadow-lg'
                        : 'border-fa-gray-200 hover:border-fa-gray-400'
                    }`}
                    style={{ backgroundColor: color }}
                  />
                ))}
              </div>
            </div>

            {/* Icon Picker */}
            <div>
              <label className="block text-sm font-medium text-fa-gray-700 mb-2">
                Icon
              </label>
              <div className="grid grid-cols-6 gap-2">
                {PREDEFINED_ICONS.map(({ icon: Icon, name }) => (
                  <motion.button
                    key={name}
                    type="button"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setValue('icon', name)}
                    className={`p-3 rounded-lg border transition-all duration-200 flex items-center justify-center ${
                      watchedIcon === name
                        ? 'border-fa-blue-400 bg-fa-blue-50'
                        : 'border-fa-gray-200 hover:border-fa-gray-400 hover:bg-fa-gray-50'
                    }`}
                  >
                    <Icon className="w-5 h-5 text-fa-gray-600" />
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Default Category Toggle */}
            <div className="flex items-center space-x-3">
              <input
                {...register('is_default')}
                type="checkbox"
                id="is_default"
                className="w-4 h-4 text-fa-blue-600 bg-fa-gray-100 border-fa-gray-300 rounded focus:ring-fa-blue-500"
              />
              <label htmlFor="is_default" className="text-sm text-fa-gray-700">
                Set as default category
              </label>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 pt-4">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="button"
                onClick={onClose}
                disabled={isLoading}
                className="flex-1 fa-button-secondary"
              >
                Cancel
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="submit"
                disabled={!isValid || isLoading}
                className="flex-1 fa-button-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>Saving...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center space-x-2">
                    <Save className="w-4 h-4" />
                    <span>{mode === 'create' ? 'Create' : 'Save'}</span>
                  </div>
                )}
              </motion.button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};
