import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm, Controller } from 'react-hook-form';
import {
  X,
  Calendar,
  Tag,
  Clock,
  Save,
  Plus,
  AlertCircle
} from 'lucide-react';
import { Todo, TodoStatus, TodoPriority } from '@shared/types';
import { todoService, CreateTodoRequest, UpdateTodoRequest } from '@renderer/services/todo.service';
import { useValidation } from '@renderer/hooks/useValidation';
import { useToast } from '@renderer/components/ui/Toast';

export interface TodoFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (todo: Todo) => void;
  todo?: Todo | null;
  mode: 'create' | 'edit';
}

interface TodoFormData {
  title: string;
  description: string;
  priority: TodoPriority;
  status: TodoStatus;
  due_date: string;
  reminder_at: string;
  tags: string[];
  estimated_duration_hours: number;
  estimated_duration_minutes: number;
}

const PRIORITY_OPTIONS: { value: TodoPriority; label: string; color: string }[] = [
  { value: 'very_low', label: 'Very Low', color: 'text-fa-gray-500' },
  { value: 'low', label: 'Low', color: 'text-fa-blue-500' },
  { value: 'medium', label: 'Medium', color: 'text-fa-yellow-500' },
  { value: 'high', label: 'High', color: 'text-fa-orange-500' },
  { value: 'very_high', label: 'Very High', color: 'text-fa-red-500' },
];

const STATUS_OPTIONS: { value: TodoStatus; label: string; color: string }[] = [
  { value: 'pending', label: 'Pending', color: 'text-fa-gray-500' },
  { value: 'in_progress', label: 'In Progress', color: 'text-fa-blue-500' },
  { value: 'completed', label: 'Completed', color: 'text-fa-green-500' },
  { value: 'archived', label: 'Archived', color: 'text-fa-gray-400' },
  { value: 'cancelled', label: 'Cancelled', color: 'text-fa-red-500' },
];

export const TodoForm: React.FC<TodoFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  todo,
  mode
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [tagInput, setTagInput] = useState('');

  // Enhanced validation and toast notifications
  const validation = useValidation({
    isCreate: mode === 'create',
    allowPastDueDates: true,
    requireUserId: false
  });
  const toast = useToast();

  const {
    register,
    handleSubmit,
    control,
    watch,
    setValue,
    reset,
    formState: { errors, isValid }
  } = useForm<TodoFormData>({
    defaultValues: {
      title: '',
      description: '',
      priority: 'medium',
      status: 'pending',
      due_date: '',
      reminder_at: '',
      tags: [],
      estimated_duration_hours: 0,
      estimated_duration_minutes: 0,
    },
    mode: 'onChange'
  });

  const watchedTags = watch('tags');

  // Reset form when modal opens/closes or todo changes
  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && todo) {
        // Parse existing todo data
        const dueDate = todo.due_date ? new Date(todo.due_date).toISOString().split('T')[0] : '';
        const reminderAt = todo.reminder_at ? new Date(todo.reminder_at).toISOString().slice(0, 16) : '';
        
        // Parse duration
        let hours = 0;
        let minutes = 0;
        if (todo.estimated_duration) {
          const match = todo.estimated_duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?/);
          if (match) {
            hours = parseInt(match[1] || '0', 10);
            minutes = parseInt(match[2] || '0', 10);
          }
        }

        reset({
          title: todo.title,
          description: todo.description || '',
          priority: todo.priority,
          status: todo.status,
          due_date: dueDate,
          reminder_at: reminderAt,
          tags: todo.tags,
          estimated_duration_hours: hours,
          estimated_duration_minutes: minutes,
        });
      } else {
        // Reset to defaults for create mode
        reset({
          title: '',
          description: '',
          priority: 'medium',
          status: 'pending',
          due_date: '',
          reminder_at: '',
          tags: [],
          estimated_duration_hours: 0,
          estimated_duration_minutes: 0,
        });
      }
      setError(null);
    }
  }, [isOpen, mode, todo, reset]);

  const formatDuration = (hours: number, minutes: number): string => {
    if (hours === 0 && minutes === 0) return '';
    return `PT${hours > 0 ? `${hours}H` : ''}${minutes > 0 ? `${minutes}M` : ''}`;
  };

  const onFormSubmit = async (data: TodoFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      const estimatedDuration = formatDuration(data.estimated_duration_hours, data.estimated_duration_minutes);

      // Prepare todo data for validation
      const todoData = {
        title: data.title.trim(),
        description: data.description.trim() || undefined,
        priority: data.priority,
        status: data.status,
        due_date: data.due_date ? new Date(data.due_date) : undefined,
        tags: data.tags,
        estimated_duration: estimatedDuration || undefined,
      };

      // Client-side validation before submission
      const validationResult = validation.validateData(todoData);
      if (!validationResult.isValid) {
        const errorMessage = validationResult.errors.length === 1
          ? validationResult.errors[0].message
          : `Please fix ${validationResult.errors.length} validation errors`;

        setError(errorMessage);
        toast.error('Validation Error', errorMessage);
        return;
      }

      if (mode === 'create') {
        const createData: CreateTodoRequest = todoData;
        const newTodo = await todoService.createTodo(createData);
        toast.success('Todo Created', 'Your todo has been created successfully');
        onSubmit(newTodo);
      } else {
        if (!todo) throw new Error('Todo is required for edit mode');

        const updateData: UpdateTodoRequest = todoData;
        const updatedTodo = await todoService.updateTodo(todo.id, updateData);
        toast.success('Todo Updated', 'Your todo has been updated successfully');
        onSubmit(updatedTodo);
      }

      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);

      // Show detailed error toast
      if (errorMessage.includes('validation')) {
        toast.error('Validation Error', errorMessage);
      } else if (errorMessage.includes('not found')) {
        toast.error('Todo Not Found', 'The todo you are trying to edit no longer exists');
      } else {
        toast.error('Operation Failed', errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddTag = () => {
    const trimmedTag = tagInput.trim();
    if (trimmedTag && !watchedTags.includes(trimmedTag)) {
      setValue('tags', [...watchedTags, trimmedTag]);
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setValue('tags', watchedTags.filter(tag => tag !== tagToRemove));
  };

  const handleTagInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      handleAddTag();
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-40 backdrop-blur-sm flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.2 }}
          className="fa-glass-panel-frosted w-full max-w-2xl max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-fa-gray-200">
            <h2 className="fa-heading-2">
              {mode === 'create' ? 'Create New Todo' : 'Edit Todo'}
            </h2>
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onClose}
              className="p-2 text-fa-gray-500 hover:text-fa-gray-700 rounded-lg hover:bg-fa-gray-100"
            >
              <X className="w-5 h-5" />
            </motion.button>
          </div>

          {/* Error Display */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm flex items-center space-x-2"
            >
              <AlertCircle className="w-4 h-4" />
              <span>{error}</span>
            </motion.div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit(onFormSubmit)} className="p-6 space-y-6">
            {/* Title */}
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-fa-gray-700 mb-2">
                Title *
              </label>
              <input
                id="title"
                type="text"
                {...register('title', { 
                  required: 'Title is required',
                  minLength: { value: 1, message: 'Title cannot be empty' },
                  maxLength: { value: 200, message: 'Title is too long' }
                })}
                className={`fa-input ${errors.title ? 'border-red-500' : ''}`}
                placeholder="What needs to be done?"
                autoFocus
              />
              {errors.title && (
                <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
              )}
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-fa-gray-700 mb-2">
                Description
              </label>
              <textarea
                id="description"
                {...register('description')}
                className="fa-input min-h-[100px] resize-y"
                placeholder="Add more details..."
                rows={4}
              />
            </div>

            {/* Priority and Status Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Priority */}
              <div>
                <label htmlFor="priority" className="block text-sm font-medium text-fa-gray-700 mb-2">
                  Priority
                </label>
                <Controller
                  name="priority"
                  control={control}
                  render={({ field }) => (
                    <select
                      {...field}
                      className="fa-input"
                    >
                      {PRIORITY_OPTIONS.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  )}
                />
              </div>

              {/* Status (only show in edit mode) */}
              {mode === 'edit' && (
                <div>
                  <label htmlFor="status" className="block text-sm font-medium text-fa-gray-700 mb-2">
                    Status
                  </label>
                  <Controller
                    name="status"
                    control={control}
                    render={({ field }) => (
                      <select
                        {...field}
                        className="fa-input"
                      >
                        {STATUS_OPTIONS.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    )}
                  />
                </div>
              )}
            </div>

            {/* Due Date and Reminder Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Due Date */}
              <div>
                <label htmlFor="due_date" className="block text-sm font-medium text-fa-gray-700 mb-2">
                  <Calendar className="w-4 h-4 inline mr-1" />
                  Due Date
                </label>
                <input
                  id="due_date"
                  type="date"
                  {...register('due_date')}
                  className="fa-input"
                />
              </div>

              {/* Reminder */}
              <div>
                <label htmlFor="reminder_at" className="block text-sm font-medium text-fa-gray-700 mb-2">
                  <Clock className="w-4 h-4 inline mr-1" />
                  Reminder
                </label>
                <input
                  id="reminder_at"
                  type="datetime-local"
                  {...register('reminder_at')}
                  className="fa-input"
                />
              </div>
            </div>

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium text-fa-gray-700 mb-2">
                <Tag className="w-4 h-4 inline mr-1" />
                Tags
              </label>
              <div className="space-y-3">
                {/* Tag Input */}
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyDown={handleTagInputKeyDown}
                    className="fa-input flex-1"
                    placeholder="Add a tag..."
                  />
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    type="button"
                    onClick={handleAddTag}
                    disabled={!tagInput.trim()}
                    className="px-4 py-2 fa-button-glass disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Plus className="w-4 h-4" />
                  </motion.button>
                </div>

                {/* Tag Display */}
                {watchedTags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {watchedTags.map((tag, index) => (
                      <motion.span
                        key={index}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-fa-blue-100 text-fa-blue-800"
                      >
                        {tag}
                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          type="button"
                          onClick={() => handleRemoveTag(tag)}
                          className="ml-2 text-fa-blue-600 hover:text-fa-blue-800"
                        >
                          <X className="w-3 h-3" />
                        </motion.button>
                      </motion.span>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Estimated Duration */}
            <div>
              <label className="block text-sm font-medium text-fa-gray-700 mb-2">
                <Clock className="w-4 h-4 inline mr-1" />
                Estimated Duration
              </label>
              <div className="flex space-x-4 items-center">
                <div className="flex items-center space-x-2">
                  <input
                    type="number"
                    {...register('estimated_duration_hours', {
                      min: { value: 0, message: 'Hours must be positive' },
                      max: { value: 999, message: 'Hours must be less than 1000' }
                    })}
                    className="fa-input w-20"
                    placeholder="0"
                    min="0"
                    max="999"
                  />
                  <span className="text-sm text-fa-gray-600">hours</span>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="number"
                    {...register('estimated_duration_minutes', {
                      min: { value: 0, message: 'Minutes must be positive' },
                      max: { value: 59, message: 'Minutes must be less than 60' }
                    })}
                    className="fa-input w-20"
                    placeholder="0"
                    min="0"
                    max="59"
                  />
                  <span className="text-sm text-fa-gray-600">minutes</span>
                </div>
              </div>
              {(errors.estimated_duration_hours || errors.estimated_duration_minutes) && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.estimated_duration_hours?.message || errors.estimated_duration_minutes?.message}
                </p>
              )}
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-fa-gray-200">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="button"
                onClick={onClose}
                disabled={isLoading}
                className="px-6 py-3 fa-button-glass disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancel
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="submit"
                disabled={isLoading || !isValid}
                className="px-6 py-3 fa-button-primary text-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>{mode === 'create' ? 'Creating...' : 'Saving...'}</span>
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    <span>{mode === 'create' ? 'Create Todo' : 'Save Changes'}</span>
                  </>
                )}
              </motion.button>
            </div>
          </form>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
