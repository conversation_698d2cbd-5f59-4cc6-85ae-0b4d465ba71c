import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Tag,
  Search,
  Edit3,
  Trash2,
  MoreVertical,
  Hash,
  X,
  Check
} from 'lucide-react';
import { Todo } from '@shared/types';
import { ConfirmationDialog } from '@renderer/components/ui/ConfirmationDialog';

interface TagManagerProps {
  todos: Todo[];
  onUpdateTodo: (todoId: string, updates: Partial<Todo>) => Promise<void>;
  onBulkUpdateTags: (oldTag: string, newTag: string) => Promise<void>;
  onDeleteTag: (tag: string) => Promise<void>;
}

interface TagStats {
  name: string;
  count: number;
  completedCount: number;
  pendingCount: number;
  todos: Todo[];
}

interface TagItemProps {
  tag: TagStats;
  onEdit: (tag: string) => void;
  onDelete: (tag: string) => void;
}

const TagItem: React.FC<TagItemProps> = ({ tag, onEdit, onDelete }) => {
  const [showMenu, setShowMenu] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const completionRate = tag.count > 0 ? (tag.completedCount / tag.count) * 100 : 0;

  const handleEdit = () => {
    onEdit(tag.name);
    setShowMenu(false);
  };

  const handleDelete = () => {
    setShowDeleteDialog(true);
    setShowMenu(false);
  };

  const confirmDelete = () => {
    onDelete(tag.name);
    setShowDeleteDialog(false);
  };

  return (
    <>
      <motion.div
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className="fa-glass-panel p-4 hover:shadow-lg transition-all duration-200"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 flex-1">
            {/* Tag Icon */}
            <div className="w-10 h-10 rounded-lg bg-fa-blue-100 flex items-center justify-center">
              <Hash className="w-5 h-5 text-fa-blue-600" />
            </div>

            {/* Tag Info */}
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h3 className="fa-body font-semibold text-fa-gray-800">
                  #{tag.name}
                </h3>
              </div>
              <div className="flex items-center space-x-4 mt-1">
                <p className="fa-caption text-fa-gray-500">
                  {tag.count} {tag.count === 1 ? 'todo' : 'todos'}
                </p>
                <div className="flex items-center space-x-1">
                  <div className="w-16 h-2 bg-fa-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-fa-success transition-all duration-300"
                      style={{ width: `${completionRate}%` }}
                    />
                  </div>
                  <span className="fa-caption text-fa-gray-500">
                    {Math.round(completionRate)}%
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Actions Menu */}
          <div className="relative">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setShowMenu(!showMenu)}
              className="p-2 rounded-lg text-fa-gray-400 hover:text-fa-gray-600 hover:bg-fa-white-glass"
            >
              <MoreVertical className="w-4 h-4" />
            </motion.button>

            <AnimatePresence>
              {showMenu && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, y: -10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: -10 }}
                  className="absolute right-0 top-full mt-2 bg-white border border-fa-gray-200 rounded-lg shadow-lg z-10 min-w-[120px]"
                >
                  <motion.button
                    whileHover={{ backgroundColor: 'rgba(0, 0, 0, 0.05)' }}
                    onClick={handleEdit}
                    className="w-full flex items-center space-x-2 px-3 py-2 text-left text-fa-gray-700 hover:bg-fa-gray-50 rounded-t-lg"
                  >
                    <Edit3 className="w-4 h-4" />
                    <span>Rename</span>
                  </motion.button>
                  <motion.button
                    whileHover={{ backgroundColor: 'rgba(220, 53, 69, 0.05)' }}
                    onClick={handleDelete}
                    className="w-full flex items-center space-x-2 px-3 py-2 text-left text-fa-error hover:bg-fa-error hover:bg-opacity-5 rounded-b-lg"
                  >
                    <Trash2 className="w-4 h-4" />
                    <span>Delete</span>
                  </motion.button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </motion.div>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={confirmDelete}
        title="Delete Tag"
        message={`Are you sure you want to delete the tag "#${tag.name}"? This will remove it from all ${tag.count} todos.`}
        confirmText="Delete"
        confirmVariant="danger"
      />
    </>
  );
};

export const TagManager: React.FC<TagManagerProps> = ({
  todos,
  onUpdateTodo,
  onBulkUpdateTags,
  onDeleteTag
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'count' | 'completion'>('count');
  const [editingTag, setEditingTag] = useState<string | null>(null);
  const [newTagName, setNewTagName] = useState('');

  // Calculate tag statistics
  const tagStats = useMemo(() => {
    const tagMap = new Map<string, TagStats>();

    todos.forEach(todo => {
      todo.tags.forEach(tag => {
        if (!tagMap.has(tag)) {
          tagMap.set(tag, {
            name: tag,
            count: 0,
            completedCount: 0,
            pendingCount: 0,
            todos: []
          });
        }

        const stats = tagMap.get(tag)!;
        stats.count++;
        stats.todos.push(todo);

        if (todo.status === 'completed') {
          stats.completedCount++;
        } else {
          stats.pendingCount++;
        }
      });
    });

    return Array.from(tagMap.values());
  }, [todos]);

  // Filter and sort tags
  const filteredAndSortedTags = useMemo(() => {
    let filtered = tagStats.filter(tag =>
      tag.name.toLowerCase().includes(searchQuery.toLowerCase())
    );

    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'count':
          return b.count - a.count;
        case 'completion':
          const aRate = a.count > 0 ? a.completedCount / a.count : 0;
          const bRate = b.count > 0 ? b.completedCount / b.count : 0;
          return bRate - aRate;
        default:
          return 0;
      }
    });

    return filtered;
  }, [tagStats, searchQuery, sortBy]);

  const handleEditTag = (tagName: string) => {
    setEditingTag(tagName);
    setNewTagName(tagName);
  };

  const handleSaveEdit = async () => {
    if (editingTag && newTagName.trim() && newTagName !== editingTag) {
      try {
        await onBulkUpdateTags(editingTag, newTagName.trim());
        setEditingTag(null);
        setNewTagName('');
      } catch (error) {
        console.error('Failed to rename tag:', error);
      }
    } else {
      setEditingTag(null);
      setNewTagName('');
    }
  };

  const handleCancelEdit = () => {
    setEditingTag(null);
    setNewTagName('');
  };

  const handleDeleteTag = async (tagName: string) => {
    try {
      await onDeleteTag(tagName);
    } catch (error) {
      console.error('Failed to delete tag:', error);
    }
  };

  if (tagStats.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="fa-glass-panel p-8">
          <Tag className="w-16 h-16 text-fa-gray-300 mx-auto mb-4" />
          <h3 className="fa-h4 text-fa-gray-600 mb-2">No Tags Yet</h3>
          <p className="fa-body text-fa-gray-500">
            Tags will appear here when you add them to your todos
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="fa-h3 text-fa-gray-800">Tag Management</h2>
        <p className="fa-body text-fa-gray-500">
          {tagStats.length} {tagStats.length === 1 ? 'tag' : 'tags'} across {todos.length} todos
        </p>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-fa-gray-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search tags..."
            className="fa-input pl-10"
          />
        </div>

        {/* Sort */}
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value as any)}
          className="fa-input w-auto"
        >
          <option value="count">Sort by Usage</option>
          <option value="name">Sort by Name</option>
          <option value="completion">Sort by Completion</option>
        </select>
      </div>

      {/* Tag List */}
      <div className="space-y-3">
        <AnimatePresence>
          {filteredAndSortedTags.map((tag) => (
            <div key={tag.name}>
              {editingTag === tag.name ? (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="fa-glass-panel p-4"
                >
                  <div className="flex items-center space-x-3">
                    <Hash className="w-5 h-5 text-fa-blue-600" />
                    <input
                      type="text"
                      value={newTagName}
                      onChange={(e) => setNewTagName(e.target.value)}
                      className="fa-input flex-1"
                      placeholder="Tag name..."
                      autoFocus
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') handleSaveEdit();
                        if (e.key === 'Escape') handleCancelEdit();
                      }}
                    />
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleSaveEdit}
                      className="p-2 rounded-lg text-fa-success hover:bg-fa-success hover:bg-opacity-10"
                    >
                      <Check className="w-4 h-4" />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleCancelEdit}
                      className="p-2 rounded-lg text-fa-gray-400 hover:bg-fa-gray-100"
                    >
                      <X className="w-4 h-4" />
                    </motion.button>
                  </div>
                </motion.div>
              ) : (
                <TagItem
                  tag={tag}
                  onEdit={handleEditTag}
                  onDelete={handleDeleteTag}
                />
              )}
            </div>
          ))}
        </AnimatePresence>
      </div>

      {/* No Results */}
      {searchQuery && filteredAndSortedTags.length === 0 && (
        <div className="text-center py-8">
          <p className="fa-body text-fa-gray-500">
            No tags found matching "{searchQuery}"
          </p>
        </div>
      )}
    </div>
  );
};
