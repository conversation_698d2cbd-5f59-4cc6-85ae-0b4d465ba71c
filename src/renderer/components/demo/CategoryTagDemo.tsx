import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Plus, Settings, Tag as TagIcon } from 'lucide-react';
import { CategoryList } from '@renderer/components/categories/CategoryList';
import { CategoryForm } from '@renderer/components/categories/CategoryForm';
import { TagManager } from '@renderer/components/tags/TagManager';
import { useTodoStore } from '@renderer/stores/todoStore';

export const CategoryTagDemo: React.FC = () => {
  const [showCategoryForm, setShowCategoryForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [activeTab, setActiveTab] = useState<'categories' | 'tags'>('categories');

  const {
    categories,
    todos,
    createCategory,
    updateCategory,
    deleteCategory,
    bulkUpdateTags,
    deleteTag,
    updateTodo
  } = useTodoStore();

  const handleCreateCategory = async (categoryData: any) => {
    try {
      await createCategory(categoryData);
      setShowCategoryForm(false);
    } catch (error) {
      console.error('Failed to create category:', error);
      throw error;
    }
  };

  const handleEditCategory = (category: any) => {
    setEditingCategory(category);
    setShowCategoryForm(true);
  };

  const handleUpdateCategory = async (categoryData: any) => {
    if (editingCategory) {
      try {
        await updateCategory(editingCategory.id, categoryData);
        setShowCategoryForm(false);
        setEditingCategory(null);
      } catch (error) {
        console.error('Failed to update category:', error);
        throw error;
      }
    }
  };

  const handleDeleteCategory = async (categoryId: string) => {
    try {
      await deleteCategory(categoryId);
    } catch (error) {
      console.error('Failed to delete category:', error);
    }
  };

  const handleBulkUpdateTags = async (oldTag: string, newTag: string) => {
    try {
      await bulkUpdateTags(oldTag, newTag);
    } catch (error) {
      console.error('Failed to update tags:', error);
    }
  };

  const handleDeleteTag = async (tag: string) => {
    try {
      await deleteTag(tag);
    } catch (error) {
      console.error('Failed to delete tag:', error);
    }
  };

  return (
    <div className="min-h-screen bg-fa-background p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="fa-h1 text-fa-gray-800 mb-2">Categories & Tags Management</h1>
          <p className="fa-body text-fa-gray-600">
            Organize your todos with categories and tags
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 mb-6 bg-fa-white-glass rounded-lg p-1">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setActiveTab('categories')}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg transition-all duration-200 ${
              activeTab === 'categories'
                ? 'bg-white shadow-md text-fa-blue-600'
                : 'text-fa-gray-600 hover:text-fa-gray-800'
            }`}
          >
            <Settings className="w-4 h-4" />
            <span>Categories</span>
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setActiveTab('tags')}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg transition-all duration-200 ${
              activeTab === 'tags'
                ? 'bg-white shadow-md text-fa-blue-600'
                : 'text-fa-gray-600 hover:text-fa-gray-800'
            }`}
          >
            <TagIcon className="w-4 h-4" />
            <span>Tags</span>
          </motion.button>
        </div>

        {/* Content */}
        <div className="fa-glass-panel p-6">
          {activeTab === 'categories' ? (
            <CategoryList
              categories={categories}
              onEdit={handleEditCategory}
              onDelete={handleDeleteCategory}
              onCreate={() => {
                setEditingCategory(null);
                setShowCategoryForm(true);
              }}
            />
          ) : (
            <TagManager
              todos={todos}
              onUpdateTodo={updateTodo}
              onBulkUpdateTags={handleBulkUpdateTags}
              onDeleteTag={handleDeleteTag}
            />
          )}
        </div>

        {/* Category Form Modal */}
        <CategoryForm
          isOpen={showCategoryForm}
          onClose={() => {
            setShowCategoryForm(false);
            setEditingCategory(null);
          }}
          onSubmit={editingCategory ? handleUpdateCategory : handleCreateCategory}
          category={editingCategory}
          mode={editingCategory ? 'edit' : 'create'}
        />
      </div>
    </div>
  );
};
